/**
 * Exemple de test E2E - Sprint 2.1
 * Démontre les bonnes pratiques de test avec <PERSON>wright
 */

import { test, expect, Page } from '@playwright/test';

// Configuration des tests
test.describe('Application E2E Tests - Sprint 2.1', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigation vers la page d'accueil avant chaque test
    await page.goto('/');
  });

  test.describe('Page d\'accueil', () => {
    test('should load the homepage successfully', async ({ page }) => {
      // Vérifier que la page se charge
      await expect(page).toHaveTitle(/Retreat And Be/);
      
      // Vérifier la présence d'éléments clés
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('nav')).toBeVisible();
    });

    test('should display navigation menu', async ({ page }) => {
      // Vérifier la présence du menu de navigation
      const nav = page.locator('nav');
      await expect(nav).toBeVisible();
      
      // Vérifier les liens de navigation principaux
      await expect(nav.locator('a[href="/"]')).toBeVisible();
      await expect(nav.locator('a[href="/about"]')).toBeVisible();
      await expect(nav.locator('a[href="/contact"]')).toBeVisible();
    });

    test('should be responsive on mobile', async ({ page }) => {
      // Simuler un viewport mobile
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Vérifier que la page s'adapte au mobile
      const mobileMenu = page.locator('[data-testid="mobile-menu"]');
      await expect(mobileMenu).toBeVisible();
    });
  });

  test.describe('Authentification', () => {
    test('should navigate to login page', async ({ page }) => {
      // Cliquer sur le lien de connexion
      await page.click('a[href="/login"]');
      
      // Vérifier la navigation
      await expect(page).toHaveURL(/.*login/);
      await expect(page.locator('h1')).toContainText('Connexion');
    });

    test('should show validation errors for empty form', async ({ page }) => {
      await page.goto('/login');
      
      // Soumettre le formulaire vide
      await page.click('button[type="submit"]');
      
      // Vérifier les messages d'erreur
      await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
    });

    test('should login with valid credentials', async ({ page }) => {
      await page.goto('/login');
      
      // Remplir le formulaire
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      
      // Soumettre le formulaire
      await page.click('button[type="submit"]');
      
      // Vérifier la redirection après connexion
      await expect(page).toHaveURL(/.*dashboard/);
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto('/login');
      
      // Remplir avec des identifiants invalides
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'wrongpassword');
      
      // Soumettre le formulaire
      await page.click('button[type="submit"]');
      
      // Vérifier le message d'erreur
      await expect(page.locator('[data-testid="login-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="login-error"]'))
        .toContainText('Identifiants invalides');
    });
  });

  test.describe('Dashboard utilisateur', () => {
    test.beforeEach(async ({ page }) => {
      // Se connecter avant chaque test du dashboard
      await loginUser(page, '<EMAIL>', 'password123');
    });

    test('should display user dashboard', async ({ page }) => {
      await expect(page).toHaveURL(/.*dashboard/);
      
      // Vérifier les éléments du dashboard
      await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="stats-widget"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
    });

    test('should allow user to update profile', async ({ page }) => {
      // Naviguer vers le profil
      await page.click('[data-testid="profile-link"]');
      await expect(page).toHaveURL(/.*profile/);
      
      // Modifier le nom
      await page.fill('input[name="name"]', 'Nouveau Nom');
      await page.click('button[type="submit"]');
      
      // Vérifier la confirmation
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      await expect(page.locator('input[name="name"]')).toHaveValue('Nouveau Nom');
    });

    test('should logout successfully', async ({ page }) => {
      // Cliquer sur le menu utilisateur
      await page.click('[data-testid="user-menu"]');
      
      // Cliquer sur déconnexion
      await page.click('[data-testid="logout-button"]');
      
      // Vérifier la redirection
      await expect(page).toHaveURL(/.*login/);
      await expect(page.locator('[data-testid="user-menu"]')).not.toBeVisible();
    });
  });

  test.describe('Fonctionnalités de recherche', () => {
    test('should perform search with results', async ({ page }) => {
      // Utiliser la barre de recherche
      await page.fill('[data-testid="search-input"]', 'retreat');
      await page.press('[data-testid="search-input"]', 'Enter');
      
      // Vérifier les résultats
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
      await expect(page.locator('[data-testid="result-item"]')).toHaveCount.greaterThan(0);
    });

    test('should show no results message', async ({ page }) => {
      // Rechercher quelque chose qui n'existe pas
      await page.fill('[data-testid="search-input"]', 'xyznoresults');
      await page.press('[data-testid="search-input"]', 'Enter');
      
      // Vérifier le message "aucun résultat"
      await expect(page.locator('[data-testid="no-results"]')).toBeVisible();
      await expect(page.locator('[data-testid="no-results"]'))
        .toContainText('Aucun résultat trouvé');
    });
  });

  test.describe('Performance et accessibilité', () => {
    test('should load page within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(3000); // Moins de 3 secondes
    });

    test('should be accessible with keyboard navigation', async ({ page }) => {
      await page.goto('/');
      
      // Tester la navigation au clavier
      await page.keyboard.press('Tab');
      await expect(page.locator(':focus')).toBeVisible();
      
      // Vérifier que tous les éléments interactifs sont accessibles
      const focusableElements = await page.locator('a, button, input, select, textarea').count();
      expect(focusableElements).toBeGreaterThan(0);
    });

    test('should have proper ARIA labels', async ({ page }) => {
      await page.goto('/');
      
      // Vérifier la présence d'attributs ARIA
      const mainContent = page.locator('main');
      await expect(mainContent).toHaveAttribute('role', 'main');
      
      const navigation = page.locator('nav');
      await expect(navigation).toHaveAttribute('role', 'navigation');
    });
  });

  test.describe('Tests de régression', () => {
    test('should maintain layout consistency', async ({ page }) => {
      await page.goto('/');
      
      // Prendre une capture d'écran pour comparaison visuelle
      await expect(page).toHaveScreenshot('homepage-layout.png');
    });

    test('should handle network errors gracefully', async ({ page }) => {
      // Simuler une erreur réseau
      await page.route('**/api/**', route => route.abort());
      
      await page.goto('/');
      
      // Vérifier que l'application gère l'erreur
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    });
  });
});

// Fonction utilitaire pour la connexion
async function loginUser(page: Page, email: string, password: string) {
  await page.goto('/login');
  await page.fill('input[name="email"]', email);
  await page.fill('input[name="password"]', password);
  await page.click('button[type="submit"]');
  await page.waitForURL(/.*dashboard/);
}

// Tests de performance spécifiques
test.describe('Tests de performance', () => {
  test('should measure Core Web Vitals', async ({ page }) => {
    await page.goto('/');
    
    // Mesurer le LCP (Largest Contentful Paint)
    const lcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });
      });
    });
    
    expect(lcp).toBeLessThan(2500); // LCP < 2.5s
  });

  test('should have minimal bundle size impact', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier la taille des ressources chargées
    const resources = await page.evaluate(() => {
      return performance.getEntriesByType('resource')
        .filter(entry => entry.name.includes('.js'))
        .reduce((total, entry) => total + (entry.transferSize || 0), 0);
    });
    
    expect(resources).toBeLessThan(1024 * 1024); // Moins de 1MB de JS
  });
});

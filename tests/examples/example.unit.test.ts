/**
 * Exemple de test unitaire - Sprint 2.1
 * Démontre les bonnes pratiques de test avec Jest
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Exemple de classe à tester
class Calculator {
  private history: number[] = [];

  add(a: number, b: number): number {
    const result = a + b;
    this.history.push(result);
    return result;
  }

  subtract(a: number, b: number): number {
    const result = a - b;
    this.history.push(result);
    return result;
  }

  multiply(a: number, b: number): number {
    const result = a * b;
    this.history.push(result);
    return result;
  }

  divide(a: number, b: number): number {
    if (b === 0) {
      throw new Error('Division by zero is not allowed');
    }
    const result = a / b;
    this.history.push(result);
    return result;
  }

  getHistory(): number[] {
    return [...this.history];
  }

  clearHistory(): void {
    this.history = [];
  }
}

// Exemple de service à tester
class UserService {
  private users: Array<{ id: number; name: string; email: string }> = [];

  async createUser(name: string, email: string): Promise<{ id: number; name: string; email: string }> {
    if (!name || !email) {
      throw new Error('Name and email are required');
    }

    if (!email.includes('@')) {
      throw new Error('Invalid email format');
    }

    const user = {
      id: this.users.length + 1,
      name,
      email
    };

    this.users.push(user);
    return user;
  }

  async getUserById(id: number): Promise<{ id: number; name: string; email: string } | null> {
    return this.users.find(user => user.id === id) || null;
  }

  async getAllUsers(): Promise<Array<{ id: number; name: string; email: string }>> {
    return [...this.users];
  }

  async deleteUser(id: number): Promise<boolean> {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) {
      return false;
    }
    this.users.splice(index, 1);
    return true;
  }
}

describe('Calculator - Tests Unitaires Sprint 2.1', () => {
  let calculator: Calculator;

  beforeEach(() => {
    calculator = new Calculator();
  });

  afterEach(() => {
    calculator.clearHistory();
  });

  describe('Addition', () => {
    it('should add two positive numbers correctly', () => {
      const result = calculator.add(2, 3);
      expect(result).toBe(5);
    });

    it('should add negative numbers correctly', () => {
      const result = calculator.add(-2, -3);
      expect(result).toBe(-5);
    });

    it('should add zero correctly', () => {
      const result = calculator.add(5, 0);
      expect(result).toBe(5);
    });

    it('should record addition in history', () => {
      calculator.add(2, 3);
      const history = calculator.getHistory();
      expect(history).toContain(5);
      expect(history).toHaveLength(1);
    });
  });

  describe('Subtraction', () => {
    it('should subtract two numbers correctly', () => {
      const result = calculator.subtract(5, 3);
      expect(result).toBe(2);
    });

    it('should handle negative results', () => {
      const result = calculator.subtract(3, 5);
      expect(result).toBe(-2);
    });
  });

  describe('Multiplication', () => {
    it('should multiply two numbers correctly', () => {
      const result = calculator.multiply(4, 5);
      expect(result).toBe(20);
    });

    it('should handle multiplication by zero', () => {
      const result = calculator.multiply(5, 0);
      expect(result).toBe(0);
    });
  });

  describe('Division', () => {
    it('should divide two numbers correctly', () => {
      const result = calculator.divide(10, 2);
      expect(result).toBe(5);
    });

    it('should throw error when dividing by zero', () => {
      expect(() => calculator.divide(10, 0)).toThrow('Division by zero is not allowed');
    });

    it('should handle decimal results', () => {
      const result = calculator.divide(10, 3);
      expect(result).toBeCloseTo(3.333, 3);
    });
  });

  describe('History Management', () => {
    it('should track operation history', () => {
      calculator.add(2, 3);
      calculator.multiply(4, 5);
      calculator.subtract(10, 2);

      const history = calculator.getHistory();
      expect(history).toEqual([5, 20, 8]);
      expect(history).toHaveLength(3);
    });

    it('should clear history correctly', () => {
      calculator.add(2, 3);
      calculator.clearHistory();
      
      const history = calculator.getHistory();
      expect(history).toEqual([]);
      expect(history).toHaveLength(0);
    });
  });
});

describe('UserService - Tests Unitaires Sprint 2.1', () => {
  let userService: UserService;

  beforeEach(() => {
    userService = new UserService();
  });

  describe('User Creation', () => {
    it('should create a user successfully', async () => {
      const user = await userService.createUser('John Doe', '<EMAIL>');
      
      expect(user).toEqual({
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>'
      });
    });

    it('should throw error for missing name', async () => {
      await expect(userService.createUser('', '<EMAIL>'))
        .rejects.toThrow('Name and email are required');
    });

    it('should throw error for missing email', async () => {
      await expect(userService.createUser('John Doe', ''))
        .rejects.toThrow('Name and email are required');
    });

    it('should throw error for invalid email format', async () => {
      await expect(userService.createUser('John Doe', 'invalid-email'))
        .rejects.toThrow('Invalid email format');
    });

    it('should assign incremental IDs', async () => {
      const user1 = await userService.createUser('John', '<EMAIL>');
      const user2 = await userService.createUser('Jane', '<EMAIL>');
      
      expect(user1.id).toBe(1);
      expect(user2.id).toBe(2);
    });
  });

  describe('User Retrieval', () => {
    beforeEach(async () => {
      await userService.createUser('John Doe', '<EMAIL>');
      await userService.createUser('Jane Smith', '<EMAIL>');
    });

    it('should get user by ID', async () => {
      const user = await userService.getUserById(1);
      
      expect(user).toEqual({
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>'
      });
    });

    it('should return null for non-existent user', async () => {
      const user = await userService.getUserById(999);
      expect(user).toBeNull();
    });

    it('should get all users', async () => {
      const users = await userService.getAllUsers();
      
      expect(users).toHaveLength(2);
      expect(users[0].name).toBe('John Doe');
      expect(users[1].name).toBe('Jane Smith');
    });
  });

  describe('User Deletion', () => {
    beforeEach(async () => {
      await userService.createUser('John Doe', '<EMAIL>');
    });

    it('should delete existing user', async () => {
      const result = await userService.deleteUser(1);
      expect(result).toBe(true);
      
      const user = await userService.getUserById(1);
      expect(user).toBeNull();
    });

    it('should return false for non-existent user', async () => {
      const result = await userService.deleteUser(999);
      expect(result).toBe(false);
    });
  });
});

# 🧪 SPRINT 2.1 - GUIDE D'AMÉLIORATION DES TESTS

**Phase 2: Stabilisation** | **Durée: 10 jours** | **12-23 Juin 2025**

## 📋 Vue d'Ensemble

Ce sprint vise à améliorer significativement la couverture et la qualité des tests pour atteindre l'objectif de **80%+ de couverture de code** avec des tests robustes et automatisés.

## 🎯 Objectifs du Sprint

### Objectifs Principaux
- ✅ **Couverture de tests**: Atteindre 80%+ sur l'ensemble du projet
- ✅ **Tests E2E complets**: Suite Playwright robuste et multi-navigateurs
- ✅ **Tests de charge**: Baseline de performance avec Artillery/K6
- ✅ **Rapports détaillés**: Dashboards et métriques de qualité

### Métriques de Succès
| Métrique | Objectif | Mesure |
|----------|----------|---------|
| Coverage branches | 80%+ | Jest coverage |
| Coverage functions | 80%+ | Jest coverage |
| Coverage lines | 80%+ | Jest coverage |
| Coverage statements | 80%+ | Jest coverage |
| Tests E2E | 100% des flows critiques | Playwright |
| Performance P95 | <200ms | Artillery |
| Taux d'erreur | <1% | Load testing |

## 🚀 Installation et Configuration

### 1. Exécution du Script Principal

```bash
# Exécuter le script d'installation et configuration
./scripts/sprint-2.1-test-improvements.sh
```

Ce script va automatiquement:
- ✅ Installer toutes les dépendances de test
- ✅ Configurer Jest avec seuils de couverture 80%
- ✅ Configurer Playwright pour tests E2E multi-navigateurs
- ✅ Créer les fichiers de setup et mocks
- ✅ Configurer les tests de charge Artillery/K6
- ✅ Mettre à jour package.json avec nouveaux scripts

### 2. Structure Créée

```
tests/
├── setup/
│   ├── jest.setup.ts          # Configuration Jest globale
│   └── ...
├── mocks/
│   ├── fileMock.js            # Mock fichiers statiques
│   └── styleMock.js           # Mock CSS
├── examples/
│   ├── example.unit.test.ts   # Exemple tests unitaires
│   └── example.e2e.test.ts    # Exemple tests E2E
├── unit/                      # Tests unitaires
├── integration/               # Tests d'intégration
├── e2e/                       # Tests E2E Playwright
└── performance/
    ├── artillery-sprint21.yml # Config Artillery
    └── k6-sprint21.js         # Config K6

scripts/
└── run-sprint21-tests.sh      # Script exécution complète

jest.config.js                 # Configuration Jest principale
playwright.config.sprint21.ts  # Configuration Playwright
```

## 🧪 Types de Tests Configurés

### 1. Tests Unitaires (Jest)

**Configuration**: `jest.config.js`
- Seuil de couverture: 80% global
- Support TypeScript avec ts-jest
- Mocks automatiques pour CSS/images
- Rapports HTML détaillés

**Commandes**:
```bash
npm run test:unit           # Tests unitaires
npm run test:coverage       # Tests avec couverture
```

**Exemple d'utilisation**:
```typescript
describe('Calculator', () => {
  it('should add numbers correctly', () => {
    const calc = new Calculator();
    expect(calc.add(2, 3)).toBe(5);
  });
});
```

### 2. Tests E2E (Playwright)

**Configuration**: `playwright.config.sprint21.ts`
- Multi-navigateurs: Chrome, Firefox, Safari
- Tests mobile: iOS/Android simulation
- Screenshots/vidéos sur échec
- Rapports HTML interactifs

**Commandes**:
```bash
npm run test:e2e:sprint21   # Tests E2E complets
npx playwright test --ui    # Interface graphique
npx playwright show-report # Voir rapports
```

**Exemple d'utilisation**:
```typescript
test('should login successfully', async ({ page }) => {
  await page.goto('/login');
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.click('button[type="submit"]');
  await expect(page).toHaveURL(/dashboard/);
});
```

### 3. Tests de Charge (Artillery/K6)

**Artillery**: `tests/performance/artillery-sprint21.yml`
- Phases: warm-up → ramp-up → sustained → peak → ramp-down
- Métriques: P95, P99, throughput, error rate
- Scénarios: endpoints critiques, APIs, stress

**K6**: `tests/performance/k6-sprint21.js`
- Configuration par étapes
- Seuils de performance: P95 < 200ms
- Métriques personnalisées

**Commandes**:
```bash
npm run test:load:artillery # Tests Artillery
npm run test:load:k6        # Tests K6
```

## 📊 Rapports et Métriques

### 1. Rapports de Couverture Jest

**Localisation**: `coverage/`
- `coverage/html-report/jest-report.html` - Rapport HTML interactif
- `coverage/lcov-report/index.html` - Rapport LCOV détaillé
- `coverage/coverage-final.json` - Données JSON brutes

**Métriques trackées**:
- Branches coverage
- Functions coverage  
- Lines coverage
- Statements coverage

### 2. Rapports Playwright E2E

**Localisation**: `test-results/`
- `test-results/playwright-html/` - Rapport HTML interactif
- `test-results/playwright-results.json` - Résultats JSON
- `test-results/playwright-junit.xml` - Format JUnit pour CI

**Fonctionnalités**:
- Timeline des tests
- Screenshots d'échecs
- Vidéos de reproduction
- Traces de debugging

### 3. Rapports de Performance

**Artillery**:
- `test-results/artillery-sprint21-report.html` - Rapport HTML
- `test-results/artillery-sprint21.json` - Données brutes

**Métriques incluses**:
- Response times (P50, P95, P99)
- Throughput (req/sec)
- Error rates
- Latency distribution

## 🔧 Scripts Disponibles

### Scripts Principaux

```bash
# Exécution complète Sprint 2.1
npm run test:sprint21

# Tests par type
npm run test:unit
npm run test:coverage
npm run test:e2e:sprint21
npm run test:load:artillery
npm run test:load:k6
```

### Scripts Utilitaires

```bash
# Installation navigateurs Playwright
npx playwright install

# Mode debug Playwright
npx playwright test --debug

# Génération rapport Artillery
npx artillery report results.json --output report.html
```

## 📈 Monitoring et CI/CD

### 1. Intégration Continue

**GitHub Actions** (exemple):
```yaml
- name: Run Sprint 2.1 Tests
  run: |
    npm run test:coverage
    npm run test:e2e:sprint21
    npm run test:load:artillery
```

### 2. Seuils de Qualité

**Échec du build si**:
- Couverture < 80%
- Tests E2E échouent
- Performance P95 > 200ms
- Taux d'erreur > 1%

### 3. Badges de Statut

```markdown
![Coverage](./coverage/badge-statements.svg)
![Tests](https://img.shields.io/badge/tests-passing-green)
![Performance](https://img.shields.io/badge/performance-optimized-blue)
```

## 🎯 Bonnes Pratiques

### 1. Tests Unitaires
- ✅ Un test = une responsabilité
- ✅ Noms descriptifs et explicites
- ✅ Arrange-Act-Assert pattern
- ✅ Mocks pour dépendances externes
- ✅ Tests des cas limites et erreurs

### 2. Tests E2E
- ✅ Tests des parcours utilisateur complets
- ✅ Data-testid pour sélecteurs stables
- ✅ Attentes explicites (waitFor)
- ✅ Nettoyage entre tests
- ✅ Tests responsive et accessibilité

### 3. Tests de Performance
- ✅ Baseline avant optimisations
- ✅ Tests réguliers en CI
- ✅ Monitoring des régressions
- ✅ Seuils réalistes et mesurables

## 🚨 Troubleshooting

### Problèmes Courants

**Jest: "Cannot find module"**
```bash
# Vérifier la configuration des paths
npm run test -- --verbose
```

**Playwright: Navigateurs manquants**
```bash
npx playwright install
```

**Artillery: Connexion refusée**
```bash
# Vérifier que le serveur est démarré
curl http://localhost:3001/health
```

### Support et Documentation

- 📖 [Jest Documentation](https://jestjs.io/docs)
- 📖 [Playwright Documentation](https://playwright.dev)
- 📖 [Artillery Documentation](https://artillery.io/docs)
- 📖 [Testing Library](https://testing-library.com)

## ✅ Checklist de Validation

### Phase 1: Configuration ✅
- [ ] Script d'installation exécuté
- [ ] Dépendances installées
- [ ] Configurations créées
- [ ] Scripts package.json mis à jour

### Phase 2: Tests Unitaires
- [ ] Couverture ≥ 80% branches
- [ ] Couverture ≥ 80% functions  
- [ ] Couverture ≥ 80% lines
- [ ] Couverture ≥ 80% statements
- [ ] Rapports HTML générés

### Phase 3: Tests E2E
- [ ] Tests multi-navigateurs passent
- [ ] Tests mobile passent
- [ ] Rapports Playwright générés
- [ ] Screenshots/vidéos disponibles

### Phase 4: Tests Performance
- [ ] Tests Artillery exécutés
- [ ] P95 < 200ms
- [ ] Taux d'erreur < 1%
- [ ] Rapports de performance générés

### Phase 5: Documentation
- [ ] Guide utilisateur créé
- [ ] Exemples de tests fournis
- [ ] Scripts documentés
- [ ] Troubleshooting disponible

---

**🎉 Sprint 2.1 - Amélioration des Tests**  
*Vers une couverture de 80%+ et des tests robustes*

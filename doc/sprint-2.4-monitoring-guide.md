# 📊 SPRINT 2.4 - GUIDE DE MONITORING AVANCÉ

**Phase 2: Stabilisation** | **Durée: 10 jours** | **18-29 Juillet 2025**

## 📋 Vue d'Ensemble

Ce sprint vise à **implémenter une stack d'observabilité complète** avec Prometheus, Grafana, Loki, Tempo et AlertManager pour un monitoring de classe mondiale.

## 🎯 Objectifs du Sprint

### Objectifs Principaux
- ✅ **Stack Observabilité**: Prometheus + Grafana + Loki + Tempo
- ✅ **Alerting Intelligent**: Escalation automatique et prédictif
- ✅ **99.9% Uptime Tracking**: Monitoring haute disponibilité
- ✅ **Business Intelligence**: Métriques ROI et KPIs temps réel

### Métriques de Succès
| Métrique | Objectif | Mesure |
|----------|----------|---------|
| Uptime | 99.9% | Prometheus |
| MTTR | <15 min | AlertManager |
| Alert Fatigue | <5% false positives | Alerting rules |
| Observability Coverage | 100% services | Stack complète |

## 🚀 Installation et Exécution

### 1. Validation des Prérequis

```bash
# Vérifier que tout est prêt pour le monitoring
./scripts/validate-sprint-2.4-setup.sh
```

### 2. Exécution du Sprint 2.4

```bash
# Lancer la configuration monitoring complète
./scripts/sprint-2.4-monitoring-advanced.sh
```

Ce script va automatiquement:
- ✅ Auditer l'infrastructure monitoring existante
- ✅ Configurer la stack observabilité complète
- ✅ Créer l'alerting intelligent avec escalation
- ✅ Configurer le tracing distribué
- ✅ Centraliser les logs avec Loki
- ✅ Générer les règles d'alertes avancées

### 3. Démarrage de la Stack

```bash
# Naviguer vers les résultats
cd monitoring-advanced/sprint-2.4-[timestamp]/

# Démarrer la stack complète
docker-compose -f docker-compose.observability.yml up -d
```

## 📊 Architecture d'Observabilité

### Stack Complète

```mermaid
graph TB
    subgraph "Applications"
        CORE[Core-API]
        BIZ[Business-Logic]
        PAY[Payment-Financial]
        COMM[Communication]
        CONTENT[Content-Management]
        AI[Analytics-Intelligence]
        HANUMAN[Hanuman-AI]
    end
    
    subgraph "Collecte"
        PROM[Prometheus]
        PROMTAIL[Promtail]
        TEMPO[Tempo]
    end
    
    subgraph "Stockage"
        LOKI[Loki Logs]
        TEMPO_STORE[Tempo Traces]
        PROM_STORE[Prometheus Metrics]
    end
    
    subgraph "Visualisation"
        GRAFANA[Grafana Dashboards]
    end
    
    subgraph "Alerting"
        ALERT[AlertManager]
        SLACK[Slack]
        EMAIL[Email]
        PAGER[PagerDuty]
    end
    
    CORE --> PROM
    BIZ --> PROM
    PAY --> PROM
    COMM --> PROMTAIL
    CONTENT --> TEMPO
    AI --> PROM
    HANUMAN --> TEMPO
    
    PROM --> PROM_STORE
    PROMTAIL --> LOKI
    TEMPO --> TEMPO_STORE
    
    PROM_STORE --> GRAFANA
    LOKI --> GRAFANA
    TEMPO_STORE --> GRAFANA
    
    PROM --> ALERT
    ALERT --> SLACK
    ALERT --> EMAIL
    ALERT --> PAGER
```

### Ports et Services

| Service | Port | Interface | Fonction |
|---------|------|-----------|----------|
| **Grafana** | 3000 | http://localhost:3000 | Dashboards et visualisation |
| **Prometheus** | 9090 | http://localhost:9090 | Métriques et requêtes |
| **AlertManager** | 9093 | http://localhost:9093 | Gestion des alertes |
| **Loki** | 3100 | http://localhost:3100 | API logs centralisés |
| **Tempo** | 3200 | http://localhost:3200 | Tracing distribué |
| **Node Exporter** | 9100 | http://localhost:9100 | Métriques système |
| **cAdvisor** | 8080 | http://localhost:8080 | Métriques containers |

## 🚨 Alerting Intelligent

### Catégories d'Alertes

#### 1. Infrastructure Critique
- **ServiceDown**: Service indisponible (30s)
- **HighMemoryUsage**: Mémoire >90% (5min)
- **HighCPUUsage**: CPU >80% (10min)

#### 2. Performance Applications
- **HighLatency**: P95 >200ms (5min)
- **HighErrorRate**: Erreurs >1% (2min)
- **LowCacheHitRate**: Cache <80% (10min)

#### 3. Business Critique
- **LowBookingRate**: Réservations faibles (30min)
- **HighCancellationRate**: Annulations >20% (15min)
- **PaymentFailureSpike**: Échecs paiement élevés (5min)

#### 4. Sécurité
- **HighFailedLoginAttempts**: >10/min (2min)
- **SuspiciousActivity**: Activité suspecte (1min)

#### 5. Prédictif (ML-based)
- **PredictedCapacityIssue**: Mémoire épuisée en 4h
- **PredictedDiskFull**: Disque plein en 24h

### Escalation Automatique

```yaml
# Exemple de route d'escalation
routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
    group_wait: 0s
    repeat_interval: 5m
    routes:
      - match:
          alertname: ServiceDown
        receiver: 'service-down-alerts'  # PagerDuty
      - match:
          alertname: HighErrorRate
        receiver: 'error-rate-alerts'    # Email DevOps
```

## 📈 Dashboards Grafana

### 1. Executive Dashboard
- **KPIs Business**: Revenus, conversions, utilisateurs actifs
- **SLA Tracking**: Uptime 99.9%, MTTR
- **Tendances**: Croissance, performance globale

### 2. Technical Dashboard
- **Services Health**: Status des 10 services consolidés
- **Performance**: Latence, throughput, erreurs
- **Infrastructure**: CPU, mémoire, réseau

### 3. Performance Dashboard
- **Core Web Vitals**: LCP, FID, CLS
- **API Performance**: P95, P99, distribution
- **Cache Performance**: Hit rates, évictions

### 4. Security Dashboard
- **Authentification**: Tentatives, succès, échecs
- **Activité Suspecte**: Détections, blocages
- **Compliance**: Métriques de conformité

### 5. User Experience Dashboard
- **Parcours Utilisateur**: Conversion, abandon
- **Satisfaction**: Temps de réponse perçu
- **Erreurs Utilisateur**: 404, timeouts

## 🔍 Tracing Distribué

### Configuration Tempo

**Protocoles supportés**:
- **Jaeger**: Port 14268 (HTTP), 14250 (gRPC)
- **OpenTelemetry**: Port 4317 (gRPC), 4318 (HTTP)

**Intégration Applications**:
```typescript
// Exemple d'instrumentation
import { trace } from '@opentelemetry/api';

const tracer = trace.getTracer('retreat-and-be');

async function createBooking(bookingData: CreateBookingDto) {
  const span = tracer.startSpan('create-booking');
  
  try {
    // Logique métier
    const booking = await this.bookingService.create(bookingData);
    span.setAttributes({
      'booking.id': booking.id,
      'booking.amount': booking.amount,
      'user.id': booking.userId
    });
    return booking;
  } catch (error) {
    span.recordException(error);
    throw error;
  } finally {
    span.end();
  }
}
```

## 📝 Logs Centralisés

### Configuration Loki

**Sources de logs**:
- **Applications**: Services consolidés
- **Containers**: Docker logs
- **Système**: Auth, kern, daemon logs

**Corrélation avec Traces**:
```json
{
  "timestamp": "2025-07-20T10:30:00Z",
  "level": "info",
  "service": "core-api",
  "message": "User authenticated successfully",
  "trace_id": "abc123def456",
  "span_id": "789ghi012jkl",
  "user_id": "user-123"
}
```

## 📊 Métriques Business

### KPIs Trackés

#### Revenus et Conversions
```promql
# Revenus par heure
sum(rate(payment_amount_total[1h]))

# Taux de conversion
rate(retreat_bookings_total[1h]) / rate(retreat_views_total[1h])

# Panier moyen
avg(payment_amount_total) by (booking_id)
```

#### Engagement Utilisateur
```promql
# Utilisateurs actifs
count(increase(user_sessions_total[24h]))

# Temps de session moyen
avg(user_session_duration_seconds)

# Pages vues par session
avg(page_views_per_session)
```

#### Performance Business
```promql
# Taux d'annulation
rate(booking_cancellations_total[1h]) / rate(retreat_bookings_total[1h])

# Satisfaction client (NPS)
avg(customer_satisfaction_score)

# Temps de résolution support
avg(support_ticket_resolution_time_seconds)
```

## 🔧 Configuration et Déploiement

### Variables d'Environnement

```bash
# Configuration SMTP pour alertes
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Intégrations
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
PAGERDUTY_ROUTING_KEY=your-pagerduty-key

# Rétention des données
PROMETHEUS_RETENTION=30d
LOKI_RETENTION=7d
TEMPO_RETENTION=24h
```

### Commandes de Gestion

```bash
# Démarrer la stack
docker-compose -f docker-compose.observability.yml up -d

# Vérifier le statut
docker-compose -f docker-compose.observability.yml ps

# Voir les logs
docker-compose -f docker-compose.observability.yml logs -f grafana

# Arrêter la stack
docker-compose -f docker-compose.observability.yml down

# Mise à jour des configurations
docker-compose -f docker-compose.observability.yml restart prometheus
```

## 🧪 Tests et Validation

### Tests d'Alertes

```bash
# Simuler une alerte de service down
curl -X POST http://localhost:9093/api/v1/alerts \
  -H "Content-Type: application/json" \
  -d '[{
    "labels": {
      "alertname": "ServiceDown",
      "service": "core-api",
      "severity": "critical"
    },
    "annotations": {
      "summary": "Test alert"
    }
  }]'
```

### Validation des Métriques

```bash
# Vérifier les métriques Prometheus
curl http://localhost:9090/api/v1/query?query=up

# Tester les logs Loki
curl http://localhost:3100/loki/api/v1/query?query={job="system"}

# Vérifier les traces Tempo
curl http://localhost:3200/api/search
```

## ✅ Checklist de Validation

### Phase 1: Infrastructure ✅
- [ ] Stack observabilité déployée
- [ ] Tous les services accessibles
- [ ] Métriques collectées
- [ ] Logs centralisés

### Phase 2: Alerting ✅
- [ ] Règles d'alertes configurées
- [ ] Escalation testée
- [ ] Notifications fonctionnelles
- [ ] Runbooks disponibles

### Phase 3: Dashboards ✅
- [ ] Dashboards Executive créés
- [ ] Dashboards Technical opérationnels
- [ ] Métriques business trackées
- [ ] Corrélation logs-metrics-traces

### Phase 4: Business Intelligence ✅
- [ ] KPIs temps réel
- [ ] Alertes business configurées
- [ ] Reports automatiques
- [ ] Analyse prédictive basique

---

**📊 Sprint 2.4 - Monitoring Avancé**  
*Vers une observabilité de classe mondiale*

#!/bin/bash

# 🧪 SPRINT 2.1 - AMÉLIORATION DES TESTS
# Script principal pour l'implémentation des améliorations de tests
# Phase 2: Stabilisation - 12-23 Juin 2025

set -e

# Couleurs pour les logs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions de logging
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔄 $1${NC}"; }
log_header() { echo -e "${CYAN}🎯 $1${NC}"; }

# Variables globales
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULTS_DIR="$PROJECT_ROOT/test-results/sprint-2.1-$TIMESTAMP"
COVERAGE_TARGET=80
JEST_CONFIG_UPDATED=false
PLAYWRIGHT_CONFIGURED=false
LOAD_TESTING_SETUP=false

# Créer les répertoires nécessaires
mkdir -p "$RESULTS_DIR"
mkdir -p "$PROJECT_ROOT/tests/unit"
mkdir -p "$PROJECT_ROOT/tests/integration"
mkdir -p "$PROJECT_ROOT/tests/e2e"
mkdir -p "$PROJECT_ROOT/tests/performance"
mkdir -p "$PROJECT_ROOT/coverage"

echo ""
log_header "🚀 SPRINT 2.1 - AMÉLIORATION DES TESTS"
echo "======================================================"
log_info "Objectif: Atteindre 80%+ de couverture de tests"
log_info "Durée: 10 jours (12-23 Juin 2025)"
log_info "Résultats: $RESULTS_DIR"
echo ""

# Fonction de nettoyage
cleanup() {
    log_info "Nettoyage des processus de test..."
    pkill -f "jest" 2>/dev/null || true
    pkill -f "playwright" 2>/dev/null || true
    pkill -f "artillery" 2>/dev/null || true
    pkill -f "k6" 2>/dev/null || true
    log_success "Nettoyage terminé"
}

trap cleanup EXIT INT TERM

# 1. VÉRIFICATION DES PRÉREQUIS
check_prerequisites() {
    log_step "Vérification des prérequis..."

    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi

    local node_version=$(node --version | cut -d'v' -f2)
    log_info "Node.js version: $node_version"

    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi

    local npm_version=$(npm --version)
    log_info "npm version: $npm_version"

    # Vérifier la structure du projet
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        log_error "package.json non trouvé dans $PROJECT_ROOT"
        exit 1
    fi

    log_success "Prérequis vérifiés"
}

# 2. INSTALLATION DES DÉPENDANCES DE TEST
install_test_dependencies() {
    log_step "Installation des dépendances de test..."

    cd "$PROJECT_ROOT"

    # Dépendances Jest et couverture
    local jest_deps=(
        "jest@^29.7.0"
        "@types/jest@^29.5.5"
        "ts-jest@^29.1.1"
        "jest-environment-jsdom@^29.7.0"
        "jest-environment-node@^29.7.0"
        "@testing-library/jest-dom@^6.1.4"
        "@testing-library/react@^13.4.0"
        "@testing-library/user-event@^14.5.1"
        "jest-coverage-badges@^1.1.2"
        "jest-html-reporters@^3.1.5"
    )

    # Dépendances Playwright
    local playwright_deps=(
        "@playwright/test@^1.40.0"
        "playwright@^1.40.0"
    )

    # Dépendances de test de charge
    local load_test_deps=(
        "artillery@^2.0.3"
        "autocannon@^7.12.0"
    )

    # Autres dépendances utiles
    local other_deps=(
        "supertest@^6.3.3"
        "nock@^13.3.8"
        "msw@^1.3.2"
        "@faker-js/faker@^8.2.0"
    )

    log_info "Installation des dépendances Jest..."
    npm install --save-dev "${jest_deps[@]}" || {
        log_error "Échec de l'installation des dépendances Jest"
        exit 1
    }

    log_info "Installation des dépendances Playwright..."
    npm install --save-dev "${playwright_deps[@]}" || {
        log_error "Échec de l'installation des dépendances Playwright"
        exit 1
    }

    log_info "Installation des dépendances de test de charge..."
    npm install --save-dev "${load_test_deps[@]}" || {
        log_warning "Certaines dépendances de test de charge ont échoué"
    }

    log_info "Installation des autres dépendances de test..."
    npm install --save-dev "${other_deps[@]}" || {
        log_warning "Certaines dépendances supplémentaires ont échoué"
    }

    # Installer les navigateurs Playwright
    log_info "Installation des navigateurs Playwright..."
    npx playwright install || {
        log_warning "Installation des navigateurs Playwright échouée"
    }

    log_success "Dépendances de test installées"
}

# 3. CONFIGURATION JEST AVANCÉE
configure_jest() {
    log_step "Configuration Jest avancée..."

    cd "$PROJECT_ROOT"

    # Créer la configuration Jest principale
    cat > jest.config.js << 'EOF'
const { pathsToModuleNameMapper } = require('ts-jest');

module.exports = {
  // Configuration de base
  preset: 'ts-jest',
  testEnvironment: 'node',

  // Répertoires de tests
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/?(*.)+(spec|test).+(ts|tsx|js)'
  ],

  // Transformation des fichiers
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest',
  },

  // Extensions de fichiers
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],

  // Mapping des modules
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/tests/mocks/fileMock.js'
  },

  // Configuration de la couverture
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,tsx,js,jsx}',
    'Projet-RB2/Backend-NestJS/src/**/*.{ts,js}',
    'Projet-RB2/Front-Audrey-V1-Main-main/src/**/*.{ts,tsx,js,jsx}',
    '!**/*.d.ts',
    '!**/*.spec.{ts,tsx,js,jsx}',
    '!**/*.test.{ts,tsx,js,jsx}',
    '!**/*.stories.{ts,tsx,js,jsx}',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/build/**',
    '!**/coverage/**',
    '!**/public/**',
    '!**/*.config.{ts,js}',
    '!**/main.{ts,js}',
    '!**/index.{ts,js}'
  ],

  // Répertoire de couverture
  coverageDirectory: '<rootDir>/coverage',

  // Formats de rapport de couverture
  coverageReporters: [
    'text',
    'text-summary',
    'lcov',
    'html',
    'json',
    'json-summary',
    'cobertura',
    'clover'
  ],

  // Seuils de couverture - Sprint 2.1 Objectif: 80%
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },

  // Configuration des reporters
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './coverage/html-report',
      filename: 'jest-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'Sprint 2.1 - Test Coverage Report'
    }]
  ],

  // Configuration de l'environnement de test
  setupFilesAfterEnv: ['<rootDir>/tests/setup/jest.setup.ts'],

  // Timeout des tests
  testTimeout: 30000,

  // Configuration des workers
  maxWorkers: '50%',

  // Mode verbose
  verbose: true,

  // Détection des handles ouverts
  detectOpenHandles: true,

  // Forcer la sortie après les tests
  forceExit: true,

  // Configuration pour les tests parallèles
  maxConcurrency: 5,

  // Ignorer les patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/'
  ],

  // Configuration TypeScript
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
      isolatedModules: true
    }
  }
};
EOF

    JEST_CONFIG_UPDATED=true
    log_success "Configuration Jest créée"
}

# 4. CONFIGURATION PLAYWRIGHT E2E
configure_playwright() {
    log_step "Configuration Playwright E2E..."

    cd "$PROJECT_ROOT"

    # Créer la configuration Playwright optimisée
    cat > playwright.config.sprint21.ts << 'EOF'
import { defineConfig, devices } from '@playwright/test';

/**
 * Configuration Playwright optimisée - Sprint 2.1
 * Objectif: Tests E2E complets et robustes
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',

  /* Configuration parallèle optimisée */
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 2 : undefined,

  /* Reporter optimisé pour Sprint 2.1 */
  reporter: [
    ['html', { outputFolder: 'test-results/playwright-html' }],
    ['json', { outputFile: 'test-results/playwright-results.json' }],
    ['junit', { outputFile: 'test-results/playwright-junit.xml' }],
    ['line'],
    ...(process.env.CI ? [['github']] : [])
  ],

  /* Configuration globale des tests */
  use: {
    /* URL de base */
    baseURL: process.env.BASE_URL || 'http://localhost:3000',

    /* Tracing pour le debugging */
    trace: 'on-first-retry',

    /* Screenshots et vidéos */
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',

    /* Timeout des actions */
    actionTimeout: 10000,
    navigationTimeout: 30000,

    /* Headers par défaut */
    extraHTTPHeaders: {
      'X-Test-Source': 'Sprint-2.1-E2E'
    }
  },

  /* Configuration des projets de test */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  /* Serveur de développement */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },

  /* Timeout global */
  timeout: 60000,

  /* Expect timeout */
  expect: {
    timeout: 10000
  },

  /* Répertoires de sortie */
  outputDir: 'test-results/playwright-artifacts',
});
EOF

    PLAYWRIGHT_CONFIGURED=true
    log_success "Configuration Playwright créée"
}

# 5. CRÉATION DES FICHIERS DE SETUP
create_test_setup() {
    log_step "Création des fichiers de setup..."

    # Créer le répertoire de setup
    mkdir -p "$PROJECT_ROOT/tests/setup"
    mkdir -p "$PROJECT_ROOT/tests/mocks"

    # Fichier de setup Jest
    cat > "$PROJECT_ROOT/tests/setup/jest.setup.ts" << 'EOF'
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';

// Configuration globale pour Testing Library
configure({
  testIdAttribute: 'data-testid',
});

// Mock des APIs globales
global.fetch = jest.fn();
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock de window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock de localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock de sessionStorage
global.sessionStorage = localStorageMock;

// Configuration des timeouts
jest.setTimeout(30000);

// Nettoyage après chaque test
afterEach(() => {
  jest.clearAllMocks();
});
EOF

    # Mock pour les fichiers statiques
    cat > "$PROJECT_ROOT/tests/mocks/fileMock.js" << 'EOF'
module.exports = 'test-file-stub';
EOF

    # Mock pour les styles CSS
    cat > "$PROJECT_ROOT/tests/mocks/styleMock.js" << 'EOF'
module.exports = {};
EOF

    log_success "Fichiers de setup créés"
}

# 6. CONFIGURATION DES TESTS DE CHARGE
configure_load_testing() {
    log_step "Configuration des tests de charge..."

    mkdir -p "$PROJECT_ROOT/tests/performance"

    # Configuration Artillery avancée pour Sprint 2.1
    cat > "$PROJECT_ROOT/tests/performance/artillery-sprint21.yml" << 'EOF'
config:
  target: 'http://localhost:3001'
  phases:
    # Phase de warm-up
    - duration: 30
      arrivalRate: 1
      name: "Warm up"
    # Montée en charge progressive
    - duration: 60
      arrivalRate: 5
      rampTo: 20
      name: "Ramp up"
    # Charge soutenue - Sprint 2.1 Target
    - duration: 180
      arrivalRate: 20
      name: "Sustained load"
    # Pic de charge
    - duration: 60
      arrivalRate: 20
      rampTo: 50
      name: "Peak load"
    # Descente
    - duration: 30
      arrivalRate: 50
      rampTo: 1
      name: "Ramp down"

  # Métriques Sprint 2.1
  metrics:
    - name: "response_time_p95"
      unit: "ms"
    - name: "response_time_p99"
      unit: "ms"
    - name: "error_rate"
      unit: "percent"
    - name: "throughput"
      unit: "req/sec"

  # Variables d'environnement
  variables:
    correlationId:
      - "sprint-2.1-load-001"
      - "sprint-2.1-load-002"
      - "sprint-2.1-load-003"
    userAgent:
      - "Sprint-2.1-Load-Test"
      - "Performance-Test-Bot"

scenarios:
  # Test des endpoints critiques
  - name: "Critical Endpoints Load Test"
    weight: 40
    flow:
      - get:
          url: "/health"
          name: "health_check"
          headers:
            X-Correlation-ID: "{{ correlationId }}"
          expect:
            - statusCode: 200
            - hasProperty: "status"
          capture:
            - json: "$.uptime"
              as: "uptime"

      - get:
          url: "/api/users"
          name: "users_endpoint"
          expect:
            - statusCode: [200, 401]

  # Test de performance des APIs
  - name: "API Performance Test"
    weight: 35
    flow:
      - post:
          url: "/api/auth/login"
          name: "login_test"
          json:
            email: "<EMAIL>"
            password: "testpassword"
          expect:
            - statusCode: [200, 400, 401]

  # Test de stress
  - name: "Stress Test"
    weight: 25
    flow:
      - get:
          url: "/metrics"
          name: "metrics_stress"
          expect:
            - statusCode: 200
EOF

    # Script de test de charge avec K6 (alternatif)
    cat > "$PROJECT_ROOT/tests/performance/k6-sprint21.js" << 'EOF'
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Métriques personnalisées
export let errorRate = new Rate('errors');

// Configuration du test
export let options = {
  stages: [
    { duration: '30s', target: 5 },   // Warm up
    { duration: '1m', target: 20 },   // Ramp up
    { duration: '3m', target: 20 },   // Stay at 20 users
    { duration: '1m', target: 50 },   // Ramp up to 50
    { duration: '30s', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'], // 95% des requêtes < 200ms
    http_req_failed: ['rate<0.1'],    // Taux d'erreur < 10%
    errors: ['rate<0.1'],
  },
};

const BASE_URL = 'http://localhost:3001';

export default function() {
  // Test health check
  let healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 100ms': (r) => r.timings.duration < 100,
  }) || errorRate.add(1);

  // Test metrics endpoint
  let metricsResponse = http.get(`${BASE_URL}/metrics`);
  check(metricsResponse, {
    'metrics status is 200': (r) => r.status === 200,
  }) || errorRate.add(1);

  sleep(1);
}
EOF

    LOAD_TESTING_SETUP=true
    log_success "Configuration des tests de charge créée"
}

# 7. CRÉATION DES SCRIPTS DE TEST
create_test_scripts() {
    log_step "Création des scripts de test..."

    # Script pour exécuter tous les tests
    cat > "$PROJECT_ROOT/scripts/run-sprint21-tests.sh" << 'EOF'
#!/bin/bash

# Script d'exécution des tests Sprint 2.1
set -e

echo "🧪 Exécution des tests Sprint 2.1"
echo "=================================="

# Tests unitaires avec couverture
echo "📊 Tests unitaires avec couverture..."
npm run test:coverage

# Tests E2E avec Playwright
echo "🎭 Tests E2E avec Playwright..."
npx playwright test --config=playwright.config.sprint21.ts

# Tests de charge avec Artillery
echo "⚡ Tests de charge avec Artillery..."
npx artillery run tests/performance/artillery-sprint21.yml --output test-results/artillery-sprint21.json

# Générer le rapport Artillery
npx artillery report test-results/artillery-sprint21.json --output test-results/artillery-sprint21-report.html

echo "✅ Tous les tests Sprint 2.1 terminés !"
echo "📊 Rapports disponibles dans test-results/"
EOF

    chmod +x "$PROJECT_ROOT/scripts/run-sprint21-tests.sh"

    log_success "Scripts de test créés"
}

# 8. MISE À JOUR DU PACKAGE.JSON
update_package_json() {
    log_step "Mise à jour du package.json..."

    cd "$PROJECT_ROOT"

    # Ajouter les scripts de test Sprint 2.1
    npm pkg set scripts.test:sprint21="./scripts/run-sprint21-tests.sh"
    npm pkg set scripts.test:coverage="jest --coverage --config=jest.config.js"
    npm pkg set scripts.test:unit="jest --config=jest.config.js"
    npm pkg set scripts.test:e2e:sprint21="playwright test --config=playwright.config.sprint21.ts"
    npm pkg set scripts.test:load:artillery="artillery run tests/performance/artillery-sprint21.yml"
    npm pkg set scripts.test:load:k6="k6 run tests/performance/k6-sprint21.js"

    log_success "Package.json mis à jour"
}

# Fonction principale
main() {
    check_prerequisites
    install_test_dependencies
    configure_jest
    configure_playwright
    create_test_setup
    configure_load_testing
    create_test_scripts
    update_package_json

    log_success "🎉 Sprint 2.1 - Configuration complète terminée !"
    log_info "📋 Résumé des configurations créées:"
    echo "  • jest.config.js - Configuration Jest avec seuil 80%"
    echo "  • playwright.config.sprint21.ts - Configuration Playwright E2E"
    echo "  • tests/setup/ - Fichiers de setup et mocks"
    echo "  • tests/performance/ - Tests de charge Artillery et K6"
    echo "  • scripts/run-sprint21-tests.sh - Script d'exécution complet"
    echo ""
    log_info "🚀 Pour démarrer les tests:"
    echo "  npm run test:sprint21"
    echo ""
    log_info "📊 Objectifs Sprint 2.1:"
    echo "  • Couverture de tests: 80%+"
    echo "  • Tests E2E complets avec Playwright"
    echo "  • Tests de charge avec Artillery/K6"
    echo "  • Rapports détaillés de performance"
}

# Exécuter le script
main "$@"

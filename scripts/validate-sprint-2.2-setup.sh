#!/bin/bash

# 🔍 VALIDATION SPRINT 2.2 - ARCHITECTURE REVIEW
# Script de validation de la consolidation d'architecture

set -e

# Couleurs pour les logs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions de logging
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔄 $1${NC}"; }
log_header() { echo -e "${CYAN}🎯 $1${NC}"; }

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VALIDATION_PASSED=true
TOTAL_CHECKS=0
PASSED_CHECKS=0

echo ""
log_header "🔍 VALIDATION SPRINT 2.2 - ARCHITECTURE REVIEW"
echo "======================================================="
log_info "Validation de la consolidation d'architecture"
echo ""

# Fonction de validation
validate_check() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if eval "$command" > /dev/null 2>&1; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description"
        VALIDATION_PASSED=false
        return 1
    fi
}

# Fonction de validation de fichier
validate_file() {
    local description="$1"
    local file_path="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if [ -f "$PROJECT_ROOT/$file_path" ]; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description - Fichier manquant: $file_path"
        VALIDATION_PASSED=false
        return 1
    fi
}

# Fonction de validation de répertoire
validate_directory() {
    local description="$1"
    local dir_path="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if [ -d "$PROJECT_ROOT/$dir_path" ]; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description - Répertoire manquant: $dir_path"
        VALIDATION_PASSED=false
        return 1
    fi
}

cd "$PROJECT_ROOT"

echo "🔧 VALIDATION DES PRÉREQUIS"
echo "============================"

# Vérification Node.js et outils
validate_check "Node.js installé" "command -v node"
validate_check "npm installé" "command -v npm"
validate_check "Docker installé" "command -v docker"

echo ""
echo "📁 VALIDATION DE LA STRUCTURE ARCHITECTURE"
echo "==========================================="

# Vérification des livrables Sprint 2.2
validate_file "Script principal Sprint 2.2" "scripts/sprint-2.2-architecture-review.sh"

# Vérification de l'architecture actuelle
validate_directory "Backend NestJS" "Projet-RB2/Backend-NestJS"
validate_directory "Frontend" "Projet-RB2/Front-Audrey-V1-Main-main"

# Vérification des services externes
validate_directory "Agent IA" "Projet-RB2/Agent IA"
validate_directory "Security Service" "Projet-RB2/Security"
validate_directory "Financial Management" "Projet-RB2/Financial-Management"
validate_directory "Hanuman System" "hanuman-unified"

echo ""
echo "📊 ANALYSE DES MODULES BACKEND"
echo "=============================="

# Compter les modules dans Backend-NestJS
if [ -d "Projet-RB2/Backend-NestJS/src" ]; then
    MODULE_COUNT=$(find "Projet-RB2/Backend-NestJS/src" -name "*.module.ts" | wc -l)
    log_info "Modules détectés dans Backend-NestJS: $MODULE_COUNT"
    
    if [ "$MODULE_COUNT" -gt 20 ]; then
        log_warning "Plus de 20 modules détectés - Consolidation nécessaire"
    else
        log_success "Nombre de modules acceptable"
    fi
fi

# Vérifier les modules principaux
CORE_MODULES=("auth" "users" "security" "audit")
BUSINESS_MODULES=("retreats" "bookings" "partners" "matching")
PAYMENT_MODULES=("payments" "coupon")

for module in "${CORE_MODULES[@]}"; do
    validate_directory "Module $module" "Projet-RB2/Backend-NestJS/src/$module"
done

echo ""
echo "🎯 VALIDATION DES OBJECTIFS SPRINT 2.2"
echo "======================================="

# Vérifier si l'analyse a été exécutée
ANALYSIS_DIR=""
if [ -d "architecture-review" ]; then
    ANALYSIS_DIR=$(find architecture-review -name "sprint-2.2-*" -type d | head -1)
fi

if [ -n "$ANALYSIS_DIR" ]; then
    log_success "Répertoire d'analyse trouvé: $ANALYSIS_DIR"
    
    # Vérifier les livrables
    validate_file "Analyse architecture actuelle" "$ANALYSIS_DIR/current-services-analysis.md"
    validate_file "Plan de consolidation" "$ANALYSIS_DIR/consolidation-plan.md"
    validate_file "Template microservice" "$ANALYSIS_DIR/microservice-template.md"
    validate_file "Diagrammes architecture" "$ANALYSIS_DIR/current-architecture-diagram.md"
    validate_file "Scripts de migration" "$ANALYSIS_DIR/migration-scripts.sh"
    validate_file "Documentation technique" "$ANALYSIS_DIR/technical-documentation.md"
else
    log_warning "Aucune analyse Sprint 2.2 trouvée - Exécuter le script principal"
fi

echo ""
echo "🔄 VALIDATION DES CAPACITÉS DE MIGRATION"
echo "========================================"

# Vérifier les outils nécessaires pour la migration
validate_check "Git installé" "command -v git"
validate_check "Kubectl installé (optionnel)" "command -v kubectl || true"

# Vérifier l'espace disque
AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
if [ "$AVAILABLE_SPACE" -gt 1000000 ]; then  # 1GB en KB
    log_success "Espace disque suffisant pour la migration"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Espace disque limité - Vérifier avant migration"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "📈 MÉTRIQUES ACTUELLES"
echo "======================"

# Calculer les métriques actuelles
if [ -d "Projet-RB2/Backend-NestJS/src" ]; then
    TOTAL_FILES=$(find "Projet-RB2/Backend-NestJS/src" -name "*.ts" | wc -l)
    TOTAL_LINES=$(find "Projet-RB2/Backend-NestJS/src" -name "*.ts" -exec wc -l {} + | tail -1 | awk '{print $1}')
    
    log_info "Fichiers TypeScript: $TOTAL_FILES"
    log_info "Lignes de code: $TOTAL_LINES"
    
    # Estimer la complexité
    if [ "$TOTAL_FILES" -gt 200 ]; then
        log_warning "Complexité élevée - Consolidation fortement recommandée"
    elif [ "$TOTAL_FILES" -gt 100 ]; then
        log_info "Complexité moyenne - Consolidation bénéfique"
    else
        log_success "Complexité acceptable"
    fi
fi

# Vérifier les dépendances
if [ -f "package.json" ]; then
    DEPENDENCIES=$(grep -c '".*":' package.json 2>/dev/null || echo "0")
    log_info "Dépendances dans package.json: $DEPENDENCIES"
fi

echo ""
echo "🎯 RECOMMANDATIONS CONSOLIDATION"
echo "================================"

log_info "Consolidation recommandée:"
echo "  📦 Core-API Service (Auth + Users + Security + Audit)"
echo "  🏢 Business-Logic Service (Retreats + Bookings + Partners + Matching)"
echo "  💳 Payment-Financial Service (Payments + Coupon + Financial-Management)"
echo "  💬 Communication Service (Messaging + Notifications + Social + Events)"
echo "  📄 Content-Management Service (Files + Moderation + Learning + Activities)"
echo "  🧠 Analytics-Intelligence Service (Analytics + Recommendation + Agent IA)"
echo "  🎮 Gamification-Engagement Service (Gamification + Social engagement)"
echo "  🔗 Integration-External Service (Integration + Phase4Excellence)"
echo "  📊 Monitoring-Operations Service (Monitoring + Performance + Health)"
echo "  🤖 Hanuman-AI-Orchestrator Service (Hanuman system optimisé)"

echo ""
echo "📊 RÉSUMÉ DE LA VALIDATION"
echo "=========================="

log_info "Checks totaux: $TOTAL_CHECKS"
log_info "Checks réussis: $PASSED_CHECKS"
log_info "Checks échoués: $((TOTAL_CHECKS - PASSED_CHECKS))"

PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
log_info "Pourcentage de réussite: $PERCENTAGE%"

echo ""

if [ "$VALIDATION_PASSED" = true ] && [ "$PERCENTAGE" -ge 80 ]; then
    log_success "🎉 VALIDATION SPRINT 2.2 RÉUSSIE !"
    echo ""
    log_info "✅ Architecture actuelle analysée"
    log_info "✅ Modules identifiés pour consolidation"
    log_info "✅ Outils de migration disponibles"
    log_info "✅ Prérequis techniques satisfaits"
    echo ""
    log_info "🚀 Prêt pour la consolidation d'architecture"
    echo ""
    log_info "Prochaines étapes:"
    echo "  1. Exécuter: ./scripts/sprint-2.2-architecture-review.sh"
    echo "  2. Réviser le plan de consolidation généré"
    echo "  3. Exécuter les scripts de migration"
    echo "  4. Valider les services consolidés"
    echo ""
    exit 0
else
    log_error "❌ VALIDATION SPRINT 2.2 ÉCHOUÉE"
    echo ""
    log_warning "Problèmes détectés dans la configuration"
    log_info "Actions recommandées:"
    
    if [ ! -f "scripts/sprint-2.2-architecture-review.sh" ]; then
        echo "  • Créer le script principal Sprint 2.2"
    fi
    
    if [ ! -d "Projet-RB2/Backend-NestJS" ]; then
        echo "  • Vérifier la structure du projet Backend"
    fi
    
    if [ "$PERCENTAGE" -lt 80 ]; then
        echo "  • Résoudre les problèmes de configuration"
    fi
    
    echo ""
    log_info "Consulter la documentation:"
    echo "  cat doc/sprint-2.2-architecture-guide.md"
    echo ""
    exit 1
fi

#!/bin/bash

# 🔍 VALIDATION SPRINT 2.3 - PERFORMANCE OPTIMIZATION
# Script de validation des optimisations de performance

set -e

# Couleurs pour les logs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions de logging
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔄 $1${NC}"; }
log_header() { echo -e "${CYAN}🎯 $1${NC}"; }

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VALIDATION_PASSED=true
TOTAL_CHECKS=0
PASSED_CHECKS=0

echo ""
log_header "🔍 VALIDATION SPRINT 2.3 - PERFORMANCE OPTIMIZATION"
echo "======================================================="
log_info "Validation des optimisations de performance"
echo ""

# Fonction de validation
validate_check() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if eval "$command" > /dev/null 2>&1; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description"
        VALIDATION_PASSED=false
        return 1
    fi
}

# Fonction de validation de fichier
validate_file() {
    local description="$1"
    local file_path="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if [ -f "$PROJECT_ROOT/$file_path" ]; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description - Fichier manquant: $file_path"
        VALIDATION_PASSED=false
        return 1
    fi
}

cd "$PROJECT_ROOT"

echo "🔧 VALIDATION DES PRÉREQUIS"
echo "============================"

# Vérification des outils de performance
validate_check "Node.js installé" "command -v node"
validate_check "npm installé" "command -v npm"
validate_check "Lighthouse installé (global)" "command -v lighthouse || npm list -g lighthouse"

echo ""
echo "📁 VALIDATION DES LIVRABLES SPRINT 2.3"
echo "======================================="

# Vérifier si l'optimisation a été exécutée
OPTIMIZATION_DIR=""
if [ -d "performance-optimization" ]; then
    OPTIMIZATION_DIR=$(find performance-optimization -name "sprint-2.3-*" -type d | head -1)
fi

if [ -n "$OPTIMIZATION_DIR" ]; then
    log_success "Répertoire d'optimisation trouvé: $OPTIMIZATION_DIR"
    
    # Vérifier les livrables frontend
    validate_file "Configuration Vite optimisée" "$OPTIMIZATION_DIR/vite.config.optimized.ts"
    validate_file "Composant LazyImage" "$OPTIMIZATION_DIR/LazyImage.optimized.tsx"
    validate_file "Service Worker optimisé" "$OPTIMIZATION_DIR/sw.optimized.js"
    
    # Vérifier les livrables backend
    validate_file "Configuration Redis" "$OPTIMIZATION_DIR/redis.optimized.conf"
    validate_file "Service de cache" "$OPTIMIZATION_DIR/CacheService.optimized.ts"
    validate_file "Intercepteur de cache" "$OPTIMIZATION_DIR/CacheInterceptor.optimized.ts"
    
    # Vérifier les livrables CDN
    validate_file "Configuration CloudFront" "$OPTIMIZATION_DIR/cloudfront.optimized.json"
    validate_file "Script optimisation images" "$OPTIMIZATION_DIR/optimize-images.sh"
    
    # Vérifier l'audit initial
    validate_file "Audit performance initial" "$OPTIMIZATION_DIR/initial-performance-audit.md"
else
    log_warning "Aucune optimisation Sprint 2.3 trouvée - Exécuter le script principal"
fi

echo ""
echo "⚡ VALIDATION DES OPTIMISATIONS FRONTEND"
echo "========================================"

# Vérifier la configuration Vite actuelle
if [ -f "vite.config.ts" ] || [ -f "Projet-RB2/vite.config.ts" ]; then
    VITE_CONFIG="vite.config.ts"
    if [ -f "Projet-RB2/vite.config.ts" ]; then
        VITE_CONFIG="Projet-RB2/vite.config.ts"
    fi
    
    log_info "Configuration Vite trouvée: $VITE_CONFIG"
    
    # Vérifier les optimisations dans la config
    if grep -q "manualChunks" "$VITE_CONFIG"; then
        log_success "Code splitting configuré"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "Code splitting non détecté"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if grep -q "terser" "$VITE_CONFIG"; then
        log_success "Minification Terser configurée"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "Minification Terser non détectée"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

# Vérifier les composants React optimisés
FRONTEND_DIR="Projet-RB2/Front-Audrey-V1-Main-main/src"
if [ -d "$FRONTEND_DIR" ]; then
    LAZY_COMPONENTS=$(find "$FRONTEND_DIR" -name "*.tsx" -o -name "*.jsx" | xargs grep -l "React.lazy\|lazy(" 2>/dev/null | wc -l)
    MEMO_COMPONENTS=$(find "$FRONTEND_DIR" -name "*.tsx" -o -name "*.jsx" | xargs grep -l "React.memo\|memo(" 2>/dev/null | wc -l)
    
    log_info "Composants avec lazy loading: $LAZY_COMPONENTS"
    log_info "Composants avec React.memo: $MEMO_COMPONENTS"
    
    if [ "$LAZY_COMPONENTS" -gt 0 ]; then
        log_success "Lazy loading implémenté"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "Lazy loading non détecté"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

echo ""
echo "🚀 VALIDATION DES OPTIMISATIONS BACKEND"
echo "======================================="

# Vérifier Redis
validate_check "Redis disponible" "command -v redis-cli || command -v redis-server"

# Vérifier les modules NestJS optimisés
BACKEND_DIR="Projet-RB2/Backend-NestJS/src"
if [ -d "$BACKEND_DIR" ]; then
    CACHE_SERVICES=$(find "$BACKEND_DIR" -name "*.ts" | xargs grep -l "CacheService\|@Cacheable" 2>/dev/null | wc -l)
    INTERCEPTORS=$(find "$BACKEND_DIR" -name "*.ts" | xargs grep -l "CacheInterceptor" 2>/dev/null | wc -l)
    
    log_info "Services avec cache: $CACHE_SERVICES"
    log_info "Intercepteurs de cache: $INTERCEPTORS"
    
    if [ "$CACHE_SERVICES" -gt 0 ]; then
        log_success "Services de cache détectés"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "Services de cache non détectés"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

echo ""
echo "📊 MÉTRIQUES DE PERFORMANCE"
echo "============================"

# Analyser la taille des bundles si disponible
if [ -d "dist" ] || [ -d "Projet-RB2/Front-Audrey-V1-Main-main/dist" ]; then
    DIST_DIR="dist"
    if [ -d "Projet-RB2/Front-Audrey-V1-Main-main/dist" ]; then
        DIST_DIR="Projet-RB2/Front-Audrey-V1-Main-main/dist"
    fi
    
    if [ -d "$DIST_DIR" ]; then
        BUNDLE_SIZE=$(du -sh "$DIST_DIR" 2>/dev/null | cut -f1 || echo "N/A")
        JS_FILES=$(find "$DIST_DIR" -name "*.js" 2>/dev/null | wc -l)
        CSS_FILES=$(find "$DIST_DIR" -name "*.css" 2>/dev/null | wc -l)
        
        log_info "Taille du bundle: $BUNDLE_SIZE"
        log_info "Fichiers JS: $JS_FILES"
        log_info "Fichiers CSS: $CSS_FILES"
        
        # Vérifier la présence de chunks
        CHUNK_FILES=$(find "$DIST_DIR" -name "*chunk*.js" 2>/dev/null | wc -l)
        if [ "$CHUNK_FILES" -gt 1 ]; then
            log_success "Code splitting détecté ($CHUNK_FILES chunks)"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            log_warning "Code splitting limité"
        fi
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    fi
fi

# Vérifier les images optimisées
IMAGES_DIRS=("public/images" "src/assets" "Projet-RB2/Front-Audrey-V1-Main-main/public/images" "Projet-RB2/Front-Audrey-V1-Main-main/src/assets")
WEBP_COUNT=0
AVIF_COUNT=0

for dir in "${IMAGES_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        WEBP_COUNT=$((WEBP_COUNT + $(find "$dir" -name "*.webp" 2>/dev/null | wc -l)))
        AVIF_COUNT=$((AVIF_COUNT + $(find "$dir" -name "*.avif" 2>/dev/null | wc -l)))
    fi
done

log_info "Images WebP: $WEBP_COUNT"
log_info "Images AVIF: $AVIF_COUNT"

if [ "$WEBP_COUNT" -gt 0 ] || [ "$AVIF_COUNT" -gt 0 ]; then
    log_success "Images modernes détectées"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Images modernes non détectées"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "🎯 RECOMMANDATIONS D'OPTIMISATION"
echo "=================================="

# Analyser les opportunités d'amélioration
log_info "Optimisations recommandées:"

if [ "$LAZY_COMPONENTS" -lt 5 ]; then
    echo "  📦 Implémenter plus de lazy loading pour les composants"
fi

if [ "$CACHE_SERVICES" -lt 3 ]; then
    echo "  🚀 Ajouter plus de services de cache backend"
fi

if [ "$WEBP_COUNT" -eq 0 ]; then
    echo "  🖼️  Convertir les images en format WebP/AVIF"
fi

if [ "$CHUNK_FILES" -lt 3 ]; then
    echo "  ✂️  Améliorer le code splitting"
fi

echo "  📊 Configurer Lighthouse CI pour monitoring continu"
echo "  🌐 Implémenter un CDN pour les assets statiques"
echo "  🔄 Ajouter un Service Worker pour le cache offline"

echo ""
echo "📊 RÉSUMÉ DE LA VALIDATION"
echo "=========================="

log_info "Checks totaux: $TOTAL_CHECKS"
log_info "Checks réussis: $PASSED_CHECKS"
log_info "Checks échoués: $((TOTAL_CHECKS - PASSED_CHECKS))"

PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
log_info "Pourcentage de réussite: $PERCENTAGE%"

echo ""

if [ "$VALIDATION_PASSED" = true ] && [ "$PERCENTAGE" -ge 70 ]; then
    log_success "🎉 VALIDATION SPRINT 2.3 RÉUSSIE !"
    echo ""
    log_info "✅ Optimisations de performance détectées"
    log_info "✅ Configuration frontend optimisée"
    log_info "✅ Services backend avec cache"
    log_info "✅ Métriques de performance mesurées"
    echo ""
    log_info "🚀 Prêt pour les tests de performance"
    echo ""
    log_info "Prochaines étapes:"
    echo "  1. Exécuter des tests Lighthouse"
    echo "  2. Mesurer les Core Web Vitals"
    echo "  3. Tester les performances en charge"
    echo "  4. Configurer le monitoring continu"
    echo ""
    exit 0
else
    log_error "❌ VALIDATION SPRINT 2.3 ÉCHOUÉE"
    echo ""
    log_warning "Optimisations de performance insuffisantes"
    log_info "Actions recommandées:"
    
    if [ ! -f "scripts/sprint-2.3-performance-optimization.sh" ]; then
        echo "  • Exécuter le script principal Sprint 2.3"
    fi
    
    if [ "$PERCENTAGE" -lt 70 ]; then
        echo "  • Implémenter plus d'optimisations"
        echo "  • Vérifier la configuration Vite"
        echo "  • Ajouter des services de cache"
    fi
    
    echo ""
    log_info "Consulter la documentation:"
    echo "  cat doc/sprint-2.3-performance-guide.md"
    echo ""
    exit 1
fi

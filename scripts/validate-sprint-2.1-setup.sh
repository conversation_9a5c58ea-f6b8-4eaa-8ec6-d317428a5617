#!/bin/bash

# 🔍 VALIDATION SPRINT 2.1 - AMÉLIORATION DES TESTS
# Script de validation de la configuration Sprint 2.1

set -e

# Couleurs pour les logs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions de logging
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔄 $1${NC}"; }
log_header() { echo -e "${CYAN}🎯 $1${NC}"; }

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VALIDATION_PASSED=true
TOTAL_CHECKS=0
PASSED_CHECKS=0

echo ""
log_header "🔍 VALIDATION SPRINT 2.1 - AMÉLIORATION DES TESTS"
echo "======================================================="
log_info "Validation de la configuration et des prérequis"
echo ""

# Fonction de validation
validate_check() {
    local description="$1"
    local command="$2"
    local expected="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if eval "$command" > /dev/null 2>&1; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description"
        VALIDATION_PASSED=false
        return 1
    fi
}

# Fonction de validation de fichier
validate_file() {
    local description="$1"
    local file_path="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if [ -f "$PROJECT_ROOT/$file_path" ]; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description - Fichier manquant: $file_path"
        VALIDATION_PASSED=false
        return 1
    fi
}

# Fonction de validation de répertoire
validate_directory() {
    local description="$1"
    local dir_path="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if [ -d "$PROJECT_ROOT/$dir_path" ]; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description - Répertoire manquant: $dir_path"
        VALIDATION_PASSED=false
        return 1
    fi
}

cd "$PROJECT_ROOT"

echo "🔧 VALIDATION DES PRÉREQUIS"
echo "============================"

# Vérification Node.js et npm
validate_check "Node.js installé" "command -v node"
validate_check "npm installé" "command -v npm"

# Vérification des versions
if command -v node > /dev/null; then
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    log_info "Version Node.js: $NODE_VERSION"
fi

if command -v npm > /dev/null; then
    NPM_VERSION=$(npm --version)
    log_info "Version npm: $NPM_VERSION"
fi

echo ""
echo "📁 VALIDATION DE LA STRUCTURE"
echo "=============================="

# Vérification des fichiers de configuration
validate_file "Configuration Jest" "jest.config.js"
validate_file "Configuration Playwright Sprint 2.1" "playwright.config.sprint21.ts"
validate_file "Package.json principal" "package.json"

# Vérification des répertoires de tests
validate_directory "Répertoire tests" "tests"
validate_directory "Répertoire tests/setup" "tests/setup"
validate_directory "Répertoire tests/mocks" "tests/mocks"
validate_directory "Répertoire tests/examples" "tests/examples"
validate_directory "Répertoire tests/performance" "tests/performance"

# Vérification des fichiers de setup
validate_file "Setup Jest" "tests/setup/jest.setup.ts"
validate_file "Mock fichiers" "tests/mocks/fileMock.js"
validate_file "Mock styles" "tests/mocks/styleMock.js"

# Vérification des exemples
validate_file "Exemple test unitaire" "tests/examples/example.unit.test.ts"
validate_file "Exemple test E2E" "tests/examples/example.e2e.test.ts"

# Vérification des configurations de performance
validate_file "Configuration Artillery" "tests/performance/artillery-sprint21.yml"
validate_file "Configuration K6" "tests/performance/k6-sprint21.js"

# Vérification des scripts
validate_file "Script principal Sprint 2.1" "scripts/sprint-2.1-test-improvements.sh"
validate_file "Script exécution tests" "scripts/run-sprint21-tests.sh"
validate_file "Guide documentation" "doc/sprint-2.1-test-improvements-guide.md"

echo ""
echo "📦 VALIDATION DES DÉPENDANCES"
echo "=============================="

# Vérification des dépendances Jest
validate_check "Jest installé" "npm list jest --depth=0"
validate_check "TypeScript Jest installé" "npm list ts-jest --depth=0"
validate_check "Testing Library installé" "npm list @testing-library/react --depth=0"

# Vérification des dépendances Playwright
validate_check "Playwright installé" "npm list @playwright/test --depth=0"

# Vérification des dépendances de performance
validate_check "Artillery installé" "npm list artillery --depth=0"

echo ""
echo "🧪 VALIDATION DES SCRIPTS PACKAGE.JSON"
echo "======================================="

# Vérification des scripts dans package.json
validate_check "Script test:sprint21" "npm run test:sprint21 --dry-run"
validate_check "Script test:coverage" "grep -q 'test:coverage' package.json"
validate_check "Script test:unit" "grep -q 'test:unit' package.json"
validate_check "Script test:e2e:sprint21" "grep -q 'test:e2e:sprint21' package.json"

echo ""
echo "⚙️ VALIDATION DES CONFIGURATIONS"
echo "================================="

# Vérification du contenu des configurations
if [ -f "jest.config.js" ]; then
    validate_check "Seuil couverture Jest 80%" "grep -q '80' jest.config.js"
    validate_check "Configuration TypeScript Jest" "grep -q 'ts-jest' jest.config.js"
    validate_check "Setup files configurés" "grep -q 'setupFilesAfterEnv' jest.config.js"
fi

if [ -f "playwright.config.sprint21.ts" ]; then
    validate_check "Configuration multi-navigateurs" "grep -q 'chromium\|firefox\|webkit' playwright.config.sprint21.ts"
    validate_check "Configuration mobile" "grep -q 'Mobile' playwright.config.sprint21.ts"
    validate_check "Rapports configurés" "grep -q 'reporter' playwright.config.sprint21.ts"
fi

echo ""
echo "🎯 VALIDATION FONCTIONNELLE"
echo "============================"

# Test de syntaxe des configurations
validate_check "Syntaxe Jest valide" "node -c jest.config.js"

# Test des exemples de tests
if command -v npx > /dev/null; then
    validate_check "Exemple test unitaire valide" "npx jest tests/examples/example.unit.test.ts --dry-run"
fi

echo ""
echo "📊 RÉSUMÉ DE LA VALIDATION"
echo "=========================="

log_info "Checks totaux: $TOTAL_CHECKS"
log_info "Checks réussis: $PASSED_CHECKS"
log_info "Checks échoués: $((TOTAL_CHECKS - PASSED_CHECKS))"

PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
log_info "Pourcentage de réussite: $PERCENTAGE%"

echo ""

if [ "$VALIDATION_PASSED" = true ]; then
    log_success "🎉 VALIDATION SPRINT 2.1 RÉUSSIE !"
    echo ""
    log_info "✅ Toutes les configurations sont en place"
    log_info "✅ Les dépendances sont installées"
    log_info "✅ Les scripts sont configurés"
    log_info "✅ Les exemples sont disponibles"
    echo ""
    log_info "🚀 Prêt pour l'exécution des tests Sprint 2.1"
    echo ""
    log_info "Commandes disponibles:"
    echo "  npm run test:sprint21        # Tests complets"
    echo "  npm run test:coverage        # Tests avec couverture"
    echo "  npm run test:e2e:sprint21    # Tests E2E"
    echo "  npm run test:load:artillery  # Tests de charge"
    echo ""
    exit 0
else
    log_error "❌ VALIDATION SPRINT 2.1 ÉCHOUÉE"
    echo ""
    log_warning "Problèmes détectés dans la configuration"
    log_info "Veuillez exécuter le script d'installation:"
    echo "  ./scripts/sprint-2.1-test-improvements.sh"
    echo ""
    log_info "Ou consulter la documentation:"
    echo "  cat doc/sprint-2.1-test-improvements-guide.md"
    echo ""
    exit 1
fi

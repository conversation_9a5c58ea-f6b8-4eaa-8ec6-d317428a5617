#!/bin/bash

# 🔍 VALIDATION SPRINT 2.4 - MONITORING AVANCÉ
# Script de validation de la stack d'observabilité

set -e

# Couleurs pour les logs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions de logging
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔄 $1${NC}"; }
log_header() { echo -e "${CYAN}🎯 $1${NC}"; }

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VALIDATION_PASSED=true
TOTAL_CHECKS=0
PASSED_CHECKS=0

echo ""
log_header "🔍 VALIDATION SPRINT 2.4 - MONITORING AVANCÉ"
echo "======================================================="
log_info "Validation de la stack d'observabilité complète"
echo ""

# Fonction de validation
validate_check() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if eval "$command" > /dev/null 2>&1; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description"
        VALIDATION_PASSED=false
        return 1
    fi
}

# Fonction de validation de fichier
validate_file() {
    local description="$1"
    local file_path="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_step "Vérification: $description"
    
    if [ -f "$PROJECT_ROOT/$file_path" ]; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description - Fichier manquant: $file_path"
        VALIDATION_PASSED=false
        return 1
    fi
}

cd "$PROJECT_ROOT"

echo "🔧 VALIDATION DES PRÉREQUIS"
echo "============================"

# Vérification des outils de monitoring
validate_check "Docker installé" "command -v docker"
validate_check "Docker Compose installé" "command -v docker-compose || command -v docker compose"

echo ""
echo "📁 VALIDATION DES LIVRABLES SPRINT 2.4"
echo "======================================="

# Vérifier si le monitoring a été configuré
MONITORING_DIR=""
if [ -d "monitoring-advanced" ]; then
    MONITORING_DIR=$(find monitoring-advanced -name "sprint-2.4-*" -type d | head -1)
fi

if [ -n "$MONITORING_DIR" ]; then
    log_success "Répertoire de monitoring trouvé: $MONITORING_DIR"
    
    # Vérifier les livrables principaux
    validate_file "Docker Compose observabilité" "$MONITORING_DIR/docker-compose.observability.yml"
    validate_file "Configuration Prometheus" "$MONITORING_DIR/prometheus/prometheus.yml"
    validate_file "Configuration Loki" "$MONITORING_DIR/loki/loki.yml"
    validate_file "Configuration Tempo" "$MONITORING_DIR/tempo/tempo.yml"
    validate_file "Configuration AlertManager" "$MONITORING_DIR/alertmanager/alertmanager.yml"
    validate_file "Configuration Promtail" "$MONITORING_DIR/promtail/promtail.yml"
    validate_file "Règles d'alertes" "$MONITORING_DIR/prometheus/rules/alerts.yml"
    validate_file "Audit monitoring" "$MONITORING_DIR/monitoring-audit.md"
else
    log_warning "Aucune configuration Sprint 2.4 trouvée - Exécuter le script principal"
fi

echo ""
echo "📊 VALIDATION DE L'INFRASTRUCTURE EXISTANTE"
echo "============================================"

# Vérifier l'infrastructure de monitoring existante
if [ -d "Projet-RB2/Backend-NestJS/src/modules/monitoring" ]; then
    log_success "Module monitoring Backend détecté"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    
    # Vérifier les services spécifiques
    if [ -f "Projet-RB2/Backend-NestJS/src/modules/monitoring/prometheus.service.ts" ]; then
        log_success "Service Prometheus existant"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "Service Prometheus non trouvé"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "Projet-RB2/Backend-NestJS/src/modules/monitoring/services/performance-monitoring.service.ts" ]; then
        log_success "Service monitoring performance existant"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "Service monitoring performance non trouvé"
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
else
    log_warning "Module monitoring Backend non détecté"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# Vérifier les configurations Grafana existantes
GRAFANA_CONFIGS=0
if [ -d "Projet-RB2/grafana" ]; then
    GRAFANA_CONFIGS=$((GRAFANA_CONFIGS + 1))
fi
if [ -d "infrastructure/grafana" ]; then
    GRAFANA_CONFIGS=$((GRAFANA_CONFIGS + 1))
fi
if [ -d "Projet-RB2/superagent/ai_engine/infra/grafana" ]; then
    GRAFANA_CONFIGS=$((GRAFANA_CONFIGS + 1))
fi

log_info "Configurations Grafana détectées: $GRAFANA_CONFIGS"
if [ "$GRAFANA_CONFIGS" -gt 0 ]; then
    log_success "Infrastructure Grafana existante"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Aucune configuration Grafana détectée"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# Vérifier les configurations Prometheus existantes
PROMETHEUS_CONFIGS=0
if [ -f "infrastructure/prometheus/prometheus.yml" ]; then
    PROMETHEUS_CONFIGS=$((PROMETHEUS_CONFIGS + 1))
fi
if [ -f "Projet-RB2/prometheus.yml" ]; then
    PROMETHEUS_CONFIGS=$((PROMETHEUS_CONFIGS + 1))
fi
if [ -f "Projet-RB2/superagent/config/prometheus.yml" ]; then
    PROMETHEUS_CONFIGS=$((PROMETHEUS_CONFIGS + 1))
fi

log_info "Configurations Prometheus détectées: $PROMETHEUS_CONFIGS"
if [ "$PROMETHEUS_CONFIGS" -gt 0 ]; then
    log_success "Infrastructure Prometheus existante"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Aucune configuration Prometheus détectée"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "🚀 VALIDATION DES SERVICES CONSOLIDÉS"
echo "====================================="

# Vérifier si les services consolidés du Sprint 2.2 sont prêts pour monitoring
CONSOLIDATED_SERVICES=("core-api" "business-logic" "payment-financial" "communication" "content-management" "analytics-intelligence" "hanuman-ai")
SERVICES_READY=0

for service in "${CONSOLIDATED_SERVICES[@]}"; do
    if [ -d "consolidated-services/$service" ] || [ -d "services/$service" ]; then
        SERVICES_READY=$((SERVICES_READY + 1))
        log_info "Service $service: Prêt pour monitoring"
    else
        log_warning "Service $service: Non trouvé"
    fi
done

log_info "Services consolidés prêts: $SERVICES_READY/${#CONSOLIDATED_SERVICES[@]}"
if [ "$SERVICES_READY" -gt 3 ]; then
    log_success "Suffisamment de services pour monitoring"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Peu de services consolidés détectés"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "⚡ VALIDATION DES OPTIMISATIONS PERFORMANCE"
echo "==========================================="

# Vérifier si les optimisations du Sprint 2.3 sont en place
PERFORMANCE_DIR=""
if [ -d "performance-optimization" ]; then
    PERFORMANCE_DIR=$(find performance-optimization -name "sprint-2.3-*" -type d | head -1)
fi

if [ -n "$PERFORMANCE_DIR" ]; then
    log_success "Optimisations performance détectées"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    
    # Vérifier les métriques de performance à monitorer
    if [ -f "$PERFORMANCE_DIR/CacheService.optimized.ts" ]; then
        log_success "Cache Redis optimisé - Métriques disponibles"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$PERFORMANCE_DIR/vite.config.optimized.ts" ]; then
        log_success "Bundle optimisé - Métriques frontend disponibles"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
else
    log_warning "Optimisations performance non détectées"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "🔍 VALIDATION DES PORTS ET SERVICES"
echo "==================================="

# Vérifier que les ports requis sont disponibles
REQUIRED_PORTS=(3000 9090 9093 3100 3200 9100 8080)
PORTS_AVAILABLE=0

for port in "${REQUIRED_PORTS[@]}"; do
    if ! netstat -tuln 2>/dev/null | grep -q ":$port " && ! ss -tuln 2>/dev/null | grep -q ":$port "; then
        PORTS_AVAILABLE=$((PORTS_AVAILABLE + 1))
        log_info "Port $port: Disponible"
    else
        log_warning "Port $port: Occupé (peut nécessiter arrêt du service)"
    fi
done

log_info "Ports disponibles: $PORTS_AVAILABLE/${#REQUIRED_PORTS[@]}"
if [ "$PORTS_AVAILABLE" -gt 5 ]; then
    log_success "Suffisamment de ports disponibles"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Plusieurs ports occupés - Vérifier les conflits"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "📈 VALIDATION DES MÉTRIQUES BUSINESS"
echo "===================================="

# Vérifier les métriques business existantes
BUSINESS_METRICS_FILES=0
if grep -r "retreat_bookings_total\|user_registrations_total\|payment_" Projet-RB2/Backend-NestJS/src/ 2>/dev/null | wc -l > 0; then
    BUSINESS_METRICS_FILES=$((BUSINESS_METRICS_FILES + 1))
fi

if [ -f "Projet-RB2/Backend-NestJS/src/modules/monitoring/prometheus.service.ts" ]; then
    if grep -q "BusinessMetrics\|userRegistrations\|retreatBookings" "Projet-RB2/Backend-NestJS/src/modules/monitoring/prometheus.service.ts"; then
        BUSINESS_METRICS_FILES=$((BUSINESS_METRICS_FILES + 1))
    fi
fi

log_info "Métriques business détectées: $BUSINESS_METRICS_FILES"
if [ "$BUSINESS_METRICS_FILES" -gt 0 ]; then
    log_success "Métriques business configurées"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "Métriques business non détectées"
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "🎯 RECOMMANDATIONS DE MONITORING"
echo "================================"

log_info "Stack d'observabilité recommandée:"
echo "  📊 Prometheus: Collecte de métriques"
echo "  📈 Grafana: Visualisation et dashboards"
echo "  📝 Loki: Logs centralisés"
echo "  🔍 Tempo: Tracing distribué"
echo "  🚨 AlertManager: Alerting intelligent"
echo "  📡 Promtail: Collecte de logs"
echo "  🖥️  Node Exporter: Métriques système"
echo "  🐳 cAdvisor: Métriques containers"

echo ""
echo "Métriques clés à surveiller:"
echo "  • Uptime et disponibilité (99.9% objectif)"
echo "  • Latence API (P95 < 200ms)"
echo "  • Taux d'erreur (< 1%)"
echo "  • Cache hit rate (> 80%)"
echo "  • Métriques business (bookings, payments)"
echo "  • Sécurité (tentatives de connexion)"

echo ""
echo "📊 RÉSUMÉ DE LA VALIDATION"
echo "=========================="

log_info "Checks totaux: $TOTAL_CHECKS"
log_info "Checks réussis: $PASSED_CHECKS"
log_info "Checks échoués: $((TOTAL_CHECKS - PASSED_CHECKS))"

PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
log_info "Pourcentage de réussite: $PERCENTAGE%"

echo ""

if [ "$VALIDATION_PASSED" = true ] && [ "$PERCENTAGE" -ge 75 ]; then
    log_success "🎉 VALIDATION SPRINT 2.4 RÉUSSIE !"
    echo ""
    log_info "✅ Infrastructure monitoring détectée"
    log_info "✅ Services consolidés prêts"
    log_info "✅ Optimisations performance en place"
    log_info "✅ Ports disponibles pour stack observabilité"
    echo ""
    log_info "🚀 Prêt pour déploiement stack observabilité"
    echo ""
    log_info "Prochaines étapes:"
    echo "  1. Exécuter: ./scripts/sprint-2.4-monitoring-advanced.sh"
    echo "  2. Démarrer la stack: docker-compose up -d"
    echo "  3. Configurer les dashboards Grafana"
    echo "  4. Tester les alertes"
    echo "  5. Valider les métriques business"
    echo ""
    exit 0
else
    log_error "❌ VALIDATION SPRINT 2.4 ÉCHOUÉE"
    echo ""
    log_warning "Infrastructure monitoring insuffisante"
    log_info "Actions recommandées:"
    
    if [ ! -f "scripts/sprint-2.4-monitoring-advanced.sh" ]; then
        echo "  • Créer le script principal Sprint 2.4"
    fi
    
    if [ "$SERVICES_READY" -lt 3 ]; then
        echo "  • Compléter la consolidation des services (Sprint 2.2)"
    fi
    
    if [ -z "$PERFORMANCE_DIR" ]; then
        echo "  • Implémenter les optimisations performance (Sprint 2.3)"
    fi
    
    if [ "$PERCENTAGE" -lt 75 ]; then
        echo "  • Résoudre les problèmes de configuration"
        echo "  • Installer Docker et Docker Compose"
        echo "  • Libérer les ports nécessaires"
    fi
    
    echo ""
    log_info "Consulter la documentation:"
    echo "  cat doc/sprint-2.4-monitoring-guide.md"
    echo ""
    exit 1
fi

/**
 * Script de mesure de performance avancé - Phase 3 Sprint 3.1
 * Utilise Lighthouse pour analyser les performances avec métriques Core Web Vitals
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

// Configuration avancée
const config = {
  url: process.env.TARGET_URL || 'http://localhost:3000',
  outputPath: './performance-reports',
  runs: 5,
  devices: ['desktop', 'mobile'],
  throttling: {
    desktop: 'desktopDense4G',
    mobile: 'mobileSlow4G'
  }
};

// Métriques Core Web Vitals
const CORE_WEB_VITALS = {
  fcp: { name: 'First Contentful Paint', good: 1800, poor: 3000 },
  lcp: { name: 'Largest Contentful Paint', good: 2500, poor: 4000 },
  fid: { name: 'First Input Delay', good: 100, poor: 300 },
  cls: { name: 'Cumulative Layout Shift', good: 0.1, poor: 0.25 },
  tbt: { name: 'Total Blocking Time', good: 200, poor: 600 },
  si: { name: 'Speed Index', good: 3400, poor: 5800 },
  tti: { name: 'Time to Interactive', good: 3800, poor: 7300 }
};

// Fonction principale
async function runAdvancedPerformanceTests() {
  console.log('🚀 Démarrage des tests de performance avancés...');
  
  if (!fs.existsSync(config.outputPath)) {
    fs.mkdirSync(config.outputPath, { recursive: true });
  }

  const allResults = {};
  
  for (const device of config.devices) {
    console.log(`\n📱 Tests ${device}...`);
    allResults[device] = [];
    
    for (let i = 0; i < config.runs; i++) {
      console.log(`📊 Exécution ${device} ${i + 1}/${config.runs}...`);
      
      const chrome = await chromeLauncher.launch({ 
        chromeFlags: ['--headless', '--no-sandbox', '--disable-dev-shm-usage'] 
      });
      
      try {
        const lighthouseConfig = {
          port: chrome.port,
          onlyCategories: ['performance'],
          settings: {
            throttling: config.throttling[device],
            emulatedFormFactor: device,
            onlyAudits: [
              'first-contentful-paint',
              'largest-contentful-paint',
              'cumulative-layout-shift',
              'total-blocking-time',
              'speed-index',
              'interactive',
              'server-response-time',
              'render-blocking-resources',
              'unused-css-rules',
              'unused-javascript',
              'modern-image-formats',
              'uses-optimized-images',
              'uses-text-compression'
            ],
          },
        };

        const runnerResult = await lighthouse(config.url, lighthouseConfig);
        
        const result = {
          run: i + 1,
          device,
          timestamp: new Date().toISOString(),
          score: Math.round(runnerResult.lhr.categories.performance.score * 100),
          metrics: extractMetrics(runnerResult.lhr),
          opportunities: extractOpportunities(runnerResult.lhr)
        };

        allResults[device].push(result);

        const reportPath = path.join(config.outputPath, `lighthouse-${device}-run-${i + 1}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(runnerResult.lhr, null, 2));
        
      } catch (error) {
        console.error(`❌ Erreur lors du test ${device} ${i + 1}:`, error.message);
      } finally {
        await chrome.kill();
      }
    }
  }

  // Analyser les résultats
  const analysis = analyzeResults(allResults);
  
  // Générer le rapport final
  const finalReport = {
    url: config.url,
    timestamp: new Date().toISOString(),
    runs: config.runs,
    devices: config.devices,
    results: allResults,
    analysis,
    recommendations: generateRecommendations(analysis),
    budgetStatus: checkPerformanceBudget(analysis)
  };

  const finalReportPath = path.join(config.outputPath, 'performance-summary.json');
  fs.writeFileSync(finalReportPath, JSON.stringify(finalReport, null, 2));

  // Générer rapport HTML
  generateHTMLReport(finalReport);

  // Afficher les résultats
  displayResults(finalReport);
  
  console.log(`✅ Tests terminés. Rapports sauvegardés dans ${config.outputPath}`);
  
  return analysis.desktop.averages.score >= 90 && analysis.mobile.averages.score >= 85;
}

// Extraire les métriques
function extractMetrics(lhr) {
  return {
    fcp: lhr.audits['first-contentful-paint']?.numericValue || 0,
    lcp: lhr.audits['largest-contentful-paint']?.numericValue || 0,
    cls: lhr.audits['cumulative-layout-shift']?.numericValue || 0,
    tbt: lhr.audits['total-blocking-time']?.numericValue || 0,
    si: lhr.audits['speed-index']?.numericValue || 0,
    tti: lhr.audits['interactive']?.numericValue || 0,
    serverResponseTime: lhr.audits['server-response-time']?.numericValue || 0
  };
}

// Extraire les opportunités
function extractOpportunities(lhr) {
  const opportunities = [];
  const audits = [
    'render-blocking-resources',
    'unused-css-rules', 
    'unused-javascript',
    'modern-image-formats',
    'uses-optimized-images',
    'uses-text-compression'
  ];
  
  audits.forEach(auditId => {
    const audit = lhr.audits[auditId];
    if (audit && audit.details && audit.details.overallSavingsMs > 100) {
      opportunities.push({
        id: auditId,
        title: audit.title,
        savings: audit.details.overallSavingsMs,
        score: audit.score
      });
    }
  });
  
  return opportunities.sort((a, b) => b.savings - a.savings);
}

// Analyser les résultats
function analyzeResults(allResults) {
  const analysis = {};
  
  for (const device of Object.keys(allResults)) {
    const results = allResults[device];
    const averages = calculateAverages(results);
    
    analysis[device] = {
      averages,
      variance: calculateVariance(results, averages),
      coreWebVitalsStatus: evaluateCoreWebVitals(averages.metrics)
    };
  }
  
  return analysis;
}

// Calculer les moyennes
function calculateAverages(results) {
  const totals = results.reduce((acc, result) => {
    acc.score += result.score;
    Object.keys(result.metrics).forEach(key => {
      acc.metrics[key] = (acc.metrics[key] || 0) + result.metrics[key];
    });
    return acc;
  }, { score: 0, metrics: {} });

  const count = results.length;
  const averages = {
    score: Math.round(totals.score / count),
    metrics: {}
  };
  
  Object.keys(totals.metrics).forEach(key => {
    averages.metrics[key] = Math.round(totals.metrics[key] / count);
  });
  
  return averages;
}

// Calculer la variance
function calculateVariance(results, averages) {
  const variance = results.reduce((acc, result) => {
    acc.score += Math.pow(result.score - averages.score, 2);
    return acc;
  }, { score: 0 });
  
  return {
    score: Math.sqrt(variance.score / results.length)
  };
}

// Évaluer Core Web Vitals
function evaluateCoreWebVitals(metrics) {
  const status = {};
  
  Object.keys(CORE_WEB_VITALS).forEach(key => {
    if (metrics[key] !== undefined) {
      const vital = CORE_WEB_VITALS[key];
      if (metrics[key] <= vital.good) {
        status[key] = 'good';
      } else if (metrics[key] <= vital.poor) {
        status[key] = 'needs-improvement';
      } else {
        status[key] = 'poor';
      }
    }
  });
  
  return status;
}

// Générer des recommandations
function generateRecommendations(analysis) {
  const recommendations = [];
  
  Object.keys(analysis).forEach(device => {
    const deviceAnalysis = analysis[device];
    const metrics = deviceAnalysis.averages.metrics;
    
    if (metrics.fcp > CORE_WEB_VITALS.fcp.good) {
      recommendations.push(`${device}: Optimiser le First Contentful Paint (${metrics.fcp}ms > ${CORE_WEB_VITALS.fcp.good}ms)`);
    }
    
    if (metrics.lcp > CORE_WEB_VITALS.lcp.good) {
      recommendations.push(`${device}: Optimiser le Largest Contentful Paint (${metrics.lcp}ms > ${CORE_WEB_VITALS.lcp.good}ms)`);
    }
    
    if (metrics.cls > CORE_WEB_VITALS.cls.good) {
      recommendations.push(`${device}: Réduire le Cumulative Layout Shift (${metrics.cls} > ${CORE_WEB_VITALS.cls.good})`);
    }
    
    if (deviceAnalysis.averages.score < (device === 'desktop' ? 90 : 85)) {
      recommendations.push(`${device}: Améliorer le score global (${deviceAnalysis.averages.score}/100)`);
    }
  });
  
  return recommendations;
}

// Vérifier le budget de performance
function checkPerformanceBudget(analysis) {
  const budget = {
    desktop: { score: 90, fcp: 1800, lcp: 2500, cls: 0.1 },
    mobile: { score: 85, fcp: 1800, lcp: 2500, cls: 0.1 }
  };
  
  const status = {};
  
  Object.keys(budget).forEach(device => {
    if (analysis[device]) {
      const deviceBudget = budget[device];
      const deviceMetrics = analysis[device].averages;
      
      status[device] = {
        score: deviceMetrics.score >= deviceBudget.score,
        fcp: deviceMetrics.metrics.fcp <= deviceBudget.fcp,
        lcp: deviceMetrics.metrics.lcp <= deviceBudget.lcp,
        cls: deviceMetrics.metrics.cls <= deviceBudget.cls,
        overall: deviceMetrics.score >= deviceBudget.score &&
                deviceMetrics.metrics.fcp <= deviceBudget.fcp &&
                deviceMetrics.metrics.lcp <= deviceBudget.lcp &&
                deviceMetrics.metrics.cls <= deviceBudget.cls
      };
    }
  });
  
  return status;
}

// Générer rapport HTML
function generateHTMLReport(report) {
  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Rapport de Performance - ${new Date().toLocaleDateString()}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .good { background-color: #d4edda; }
        .needs-improvement { background-color: #fff3cd; }
        .poor { background-color: #f8d7da; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Rapport de Performance</h1>
    <p><strong>URL:</strong> ${report.url}</p>
    <p><strong>Date:</strong> ${new Date(report.timestamp).toLocaleString()}</p>
    
    <h2>Résultats par Device</h2>
    ${Object.keys(report.analysis).map(device => `
        <h3>${device.charAt(0).toUpperCase() + device.slice(1)}</h3>
        <p><strong>Score moyen:</strong> ${report.analysis[device].averages.score}/100</p>
        <div class="metrics">
            ${Object.keys(report.analysis[device].averages.metrics).map(metric => {
              const value = report.analysis[device].averages.metrics[metric];
              const status = report.analysis[device].coreWebVitalsStatus[metric] || 'unknown';
              return `<div class="metric ${status}"><strong>${metric.toUpperCase()}:</strong> ${value}${metric === 'cls' ? '' : 'ms'}</div>`;
            }).join('')}
        </div>
    `).join('')}
    
    <h2>Recommandations</h2>
    <ul>
        ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
    </ul>
</body>
</html>`;
  
  fs.writeFileSync(path.join(config.outputPath, 'performance-report.html'), html);
}

// Afficher les résultats
function displayResults(report) {
  console.log('\n📊 Résultats des tests de performance:');
  console.log('=====================================');
  console.log(`URL testée: ${report.url}`);
  console.log(`Nombre de tests: ${report.runs} par device`);
  
  Object.keys(report.analysis).forEach(device => {
    const analysis = report.analysis[device];
    console.log(`\n📱 ${device.toUpperCase()}:`);
    console.log(`  Score moyen: ${analysis.averages.score}/100`);
    console.log(`  FCP: ${analysis.averages.metrics.fcp}ms`);
    console.log(`  LCP: ${analysis.averages.metrics.lcp}ms`);
    console.log(`  CLS: ${analysis.averages.metrics.cls}`);
    console.log(`  TBT: ${analysis.averages.metrics.tbt}ms`);
  });
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 Recommandations:');
    report.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  }
  
  console.log('\n🎯 Budget de Performance:');
  Object.keys(report.budgetStatus).forEach(device => {
    const status = report.budgetStatus[device];
    console.log(`  ${device}: ${status.overall ? '✅ RESPECTÉ' : '❌ NON RESPECTÉ'}`);
  });
}

// Exécuter les tests
if (require.main === module) {
  runAdvancedPerformanceTests().catch(console.error);
}

module.exports = { runAdvancedPerformanceTests };

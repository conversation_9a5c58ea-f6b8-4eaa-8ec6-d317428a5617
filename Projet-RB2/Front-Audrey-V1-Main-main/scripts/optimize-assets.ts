/**
 * Script d'Optimisation des Assets - Phase 3 Sprint 3.1
 * Date de création: 29 Mai 2025
 * 
 * Optimise automatiquement :
 * - Images (compression, formats modernes)
 * - Fonts (subsetting, formats optimaux)
 * - Assets statiques
 * - Génération des manifests pour CDN
 */

import fs from 'fs/promises';
import path from 'path';
import sharp from 'sharp';
import { glob } from 'glob';
import { createHash } from 'crypto';

interface OptimizationConfig {
  inputDir: string;
  outputDir: string;
  imageQuality: number;
  enableWebP: boolean;
  enableAvif: boolean;
  generateManifest: boolean;
}

interface AssetManifest {
  version: string;
  timestamp: string;
  assets: Record<string, {
    original: string;
    optimized: string[];
    size: number;
    hash: string;
  }>;
}

class AssetOptimizer {
  private config: OptimizationConfig;
  private manifest: AssetManifest;

  constructor(config: OptimizationConfig) {
    this.config = config;
    this.manifest = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      assets: {},
    };
  }

  /**
   * Lance l'optimisation complète des assets
   */
  async optimize(): Promise<void> {
    console.log('🚀 Démarrage de l\'optimisation des assets...');

    try {
      // Créer le dossier de sortie
      await this.ensureOutputDir();

      // Optimiser les images
      await this.optimizeImages();

      // Optimiser les fonts
      await this.optimizeFonts();

      // Générer le manifest
      if (this.config.generateManifest) {
        await this.generateManifest();
      }

      console.log('✅ Optimisation terminée avec succès !');
      this.printStats();
    } catch (error) {
      console.error('❌ Erreur lors de l\'optimisation:', error);
      process.exit(1);
    }
  }

  /**
   * Optimise toutes les images
   */
  private async optimizeImages(): Promise<void> {
    console.log('📸 Optimisation des images...');

    const imagePatterns = [
      `${this.config.inputDir}/**/*.{jpg,jpeg,png,gif,svg}`,
      `${this.config.inputDir}/**/*.{JPG,JPEG,PNG,GIF,SVG}`,
    ];

    const imageFiles = await glob(imagePatterns);
    console.log(`Trouvé ${imageFiles.length} images à optimiser`);

    for (const imagePath of imageFiles) {
      await this.optimizeImage(imagePath);
    }
  }

  /**
   * Optimise une image individuelle
   */
  private async optimizeImage(imagePath: string): Promise<void> {
    const relativePath = path.relative(this.config.inputDir, imagePath);
    const outputPath = path.join(this.config.outputDir, relativePath);
    const outputDir = path.dirname(outputPath);

    // Créer le dossier de sortie
    await fs.mkdir(outputDir, { recursive: true });

    const ext = path.extname(imagePath).toLowerCase();
    const baseName = path.basename(imagePath, ext);
    const baseOutputPath = path.join(outputDir, baseName);

    const optimizedFiles: string[] = [];

    try {
      // Lire l'image originale
      const imageBuffer = await fs.readFile(imagePath);
      const image = sharp(imageBuffer);
      const metadata = await image.metadata();

      // Calculer le hash de l'image originale
      const hash = createHash('md5').update(imageBuffer).digest('hex').substring(0, 8);

      if (ext === '.svg') {
        // Pour les SVG, juste copier (optimisation SVG peut être ajoutée)
        const svgOutputPath = `${baseOutputPath}-${hash}.svg`;
        await fs.copyFile(imagePath, svgOutputPath);
        optimizedFiles.push(svgOutputPath);
      } else {
        // Optimiser en format original
        const originalOutputPath = `${baseOutputPath}-${hash}${ext}`;
        await image
          .jpeg({ quality: this.config.imageQuality, progressive: true })
          .png({ quality: this.config.imageQuality, progressive: true })
          .toFile(originalOutputPath);
        optimizedFiles.push(originalOutputPath);

        // Générer WebP si activé
        if (this.config.enableWebP) {
          const webpOutputPath = `${baseOutputPath}-${hash}.webp`;
          await image
            .webp({ quality: this.config.imageQuality })
            .toFile(webpOutputPath);
          optimizedFiles.push(webpOutputPath);
        }

        // Générer AVIF si activé
        if (this.config.enableAvif) {
          const avifOutputPath = `${baseOutputPath}-${hash}.avif`;
          await image
            .avif({ quality: this.config.imageQuality })
            .toFile(avifOutputPath);
          optimizedFiles.push(avifOutputPath);
        }

        // Générer des tailles responsives pour les grandes images
        if (metadata.width && metadata.width > 800) {
          const sizes = [400, 800, 1200];
          for (const size of sizes) {
            if (size < metadata.width) {
              const resizedPath = `${baseOutputPath}-${hash}-${size}w${ext}`;
              await image
                .resize(size, null, { withoutEnlargement: true })
                .jpeg({ quality: this.config.imageQuality })
                .png({ quality: this.config.imageQuality })
                .toFile(resizedPath);
              optimizedFiles.push(resizedPath);

              if (this.config.enableWebP) {
                const webpResizedPath = `${baseOutputPath}-${hash}-${size}w.webp`;
                await image
                  .resize(size, null, { withoutEnlargement: true })
                  .webp({ quality: this.config.imageQuality })
                  .toFile(webpResizedPath);
                optimizedFiles.push(webpResizedPath);
              }
            }
          }
        }
      }

      // Ajouter au manifest
      const originalSize = imageBuffer.length;
      this.manifest.assets[relativePath] = {
        original: imagePath,
        optimized: optimizedFiles,
        size: originalSize,
        hash,
      };

      console.log(`✓ ${relativePath} optimisé (${optimizedFiles.length} variantes)`);
    } catch (error) {
      console.error(`❌ Erreur lors de l'optimisation de ${imagePath}:`, error);
    }
  }

  /**
   * Optimise les fonts
   */
  private async optimizeFonts(): Promise<void> {
    console.log('🔤 Optimisation des fonts...');

    const fontPatterns = [
      `${this.config.inputDir}/**/*.{woff,woff2,ttf,otf}`,
      `${this.config.inputDir}/**/*.{WOFF,WOFF2,TTF,OTF}`,
    ];

    const fontFiles = await glob(fontPatterns);
    console.log(`Trouvé ${fontFiles.length} fonts à optimiser`);

    for (const fontPath of fontFiles) {
      await this.optimizeFont(fontPath);
    }
  }

  /**
   * Optimise une font individuelle
   */
  private async optimizeFont(fontPath: string): Promise<void> {
    const relativePath = path.relative(this.config.inputDir, fontPath);
    const outputPath = path.join(this.config.outputDir, relativePath);
    const outputDir = path.dirname(outputPath);

    // Créer le dossier de sortie
    await fs.mkdir(outputDir, { recursive: true });

    try {
      // Pour l'instant, juste copier les fonts
      // TODO: Implémenter le subsetting avec fonttools
      await fs.copyFile(fontPath, outputPath);

      const fontBuffer = await fs.readFile(fontPath);
      const hash = createHash('md5').update(fontBuffer).digest('hex').substring(0, 8);

      this.manifest.assets[relativePath] = {
        original: fontPath,
        optimized: [outputPath],
        size: fontBuffer.length,
        hash,
      };

      console.log(`✓ ${relativePath} copié`);
    } catch (error) {
      console.error(`❌ Erreur lors de l'optimisation de ${fontPath}:`, error);
    }
  }

  /**
   * Génère le manifest des assets
   */
  private async generateManifest(): Promise<void> {
    console.log('📄 Génération du manifest...');

    const manifestPath = path.join(this.config.outputDir, 'asset-manifest.json');
    await fs.writeFile(manifestPath, JSON.stringify(this.manifest, null, 2));

    console.log(`✓ Manifest généré: ${manifestPath}`);
  }

  /**
   * Assure que le dossier de sortie existe
   */
  private async ensureOutputDir(): Promise<void> {
    await fs.mkdir(this.config.outputDir, { recursive: true });
  }

  /**
   * Affiche les statistiques d'optimisation
   */
  private printStats(): void {
    const totalAssets = Object.keys(this.manifest.assets).length;
    const totalSize = Object.values(this.manifest.assets)
      .reduce((sum, asset) => sum + asset.size, 0);

    console.log('\n📊 Statistiques d\'optimisation:');
    console.log(`   Assets traités: ${totalAssets}`);
    console.log(`   Taille totale: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Dossier de sortie: ${this.config.outputDir}`);
  }
}

// Configuration par défaut
const defaultConfig: OptimizationConfig = {
  inputDir: './public',
  outputDir: './dist/optimized-assets',
  imageQuality: 85,
  enableWebP: true,
  enableAvif: true,
  generateManifest: true,
};

// Exécution du script
if (require.main === module) {
  const optimizer = new AssetOptimizer(defaultConfig);
  optimizer.optimize();
}

export { AssetOptimizer, OptimizationConfig };

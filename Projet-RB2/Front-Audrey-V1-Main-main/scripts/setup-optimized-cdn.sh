#!/bin/bash

# Script de configuration CDN optimisé - Phase 3 Sprint 3.1
# Configuration CloudFront avec optimisations avancées pour performance

set -e

echo "🚀 Configuration du CDN CloudFront optimisé..."

# Variables de configuration
DISTRIBUTION_CONFIG="cloudfront-distribution.json"
BUCKET_NAME="${CDN_BUCKET_NAME:-retreatandbe-assets-optimized}"
DOMAIN_NAME="${CDN_DOMAIN_NAME:-cdn-optimized.retreatandbe.com}"
REGION="${AWS_REGION:-us-east-1}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
    log "Vérification des prérequis..."
    
    if ! command -v aws &> /dev/null; then
        error "AWS CLI n'est pas installé. Veuillez l'installer d'abord."
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        error "jq n'est pas installé. Veuillez l'installer d'abord."
        exit 1
    fi
    
    # Vérifier les credentials AWS
    if ! aws sts get-caller-identity &> /dev/null; then
        error "Credentials AWS non configurés. Veuillez configurer AWS CLI."
        exit 1
    fi
    
    success "Prérequis validés"
}

# Créer le bucket S3 avec configuration optimisée
setup_s3_bucket() {
    log "Configuration du bucket S3 optimisé..."
    
    # Créer le bucket
    if aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
        warning "Bucket $BUCKET_NAME existe déjà"
    else
        aws s3 mb "s3://$BUCKET_NAME" --region "$REGION"
        success "Bucket $BUCKET_NAME créé"
    fi
    
    # Configuration CORS pour CDN
    cat > cors-config.json << EOF
{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "HEAD"],
            "AllowedOrigins": ["*"],
            "ExposeHeaders": ["ETag"],
            "MaxAgeSeconds": 3600
        }
    ]
}
EOF
    
    aws s3api put-bucket-cors --bucket "$BUCKET_NAME" --cors-configuration file://cors-config.json
    success "Configuration CORS appliquée"
    
    # Configuration du cache
    cat > lifecycle-config.json << EOF
{
    "Rules": [
        {
            "ID": "OptimizeStorage",
            "Status": "Enabled",
            "Filter": {"Prefix": ""},
            "Transitions": [
                {
                    "Days": 30,
                    "StorageClass": "STANDARD_IA"
                },
                {
                    "Days": 90,
                    "StorageClass": "GLACIER"
                }
            ]
        }
    ]
}
EOF
    
    aws s3api put-bucket-lifecycle-configuration --bucket "$BUCKET_NAME" --lifecycle-configuration file://lifecycle-config.json
    success "Configuration lifecycle appliquée"
    
    # Politique de bucket optimisée
    cat > bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
        }
    ]
}
EOF
    
    aws s3api put-bucket-policy --bucket "$BUCKET_NAME" --policy file://bucket-policy.json
    success "Politique de bucket appliquée"
}

# Créer la distribution CloudFront optimisée
setup_cloudfront() {
    log "Configuration de CloudFront optimisée..."
    
    # Générer la configuration CloudFront
    cat > "$DISTRIBUTION_CONFIG" << EOF
{
    "CallerReference": "retreatandbe-cdn-optimized-$(date +%s)",
    "Comment": "CDN optimisé pour Retreat And Be - Phase 3",
    "DefaultRootObject": "index.html",
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "S3-$BUCKET_NAME",
                "DomainName": "$BUCKET_NAME.s3.$REGION.amazonaws.com",
                "S3OriginConfig": {
                    "OriginAccessIdentity": ""
                },
                "ConnectionAttempts": 3,
                "ConnectionTimeout": 10
            }
        ]
    },
    "DefaultCacheBehavior": {
        "TargetOriginId": "S3-$BUCKET_NAME",
        "ViewerProtocolPolicy": "redirect-to-https",
        "MinTTL": 0,
        "DefaultTTL": 86400,
        "MaxTTL": 31536000,
        "Compress": true,
        "ForwardedValues": {
            "QueryString": true,
            "Cookies": {
                "Forward": "none"
            },
            "Headers": {
                "Quantity": 4,
                "Items": ["Accept", "Accept-Encoding", "CloudFront-Viewer-Country", "CloudFront-Is-Mobile-Viewer"]
            }
        },
        "TrustedSigners": {
            "Enabled": false,
            "Quantity": 0
        }
    },
    "CacheBehaviors": {
        "Quantity": 4,
        "Items": [
            {
                "PathPattern": "*.js",
                "TargetOriginId": "S3-$BUCKET_NAME",
                "ViewerProtocolPolicy": "redirect-to-https",
                "MinTTL": 0,
                "DefaultTTL": 31536000,
                "MaxTTL": 31536000,
                "Compress": true,
                "ForwardedValues": {
                    "QueryString": false,
                    "Cookies": {"Forward": "none"}
                }
            },
            {
                "PathPattern": "*.css",
                "TargetOriginId": "S3-$BUCKET_NAME",
                "ViewerProtocolPolicy": "redirect-to-https",
                "MinTTL": 0,
                "DefaultTTL": 31536000,
                "MaxTTL": 31536000,
                "Compress": true,
                "ForwardedValues": {
                    "QueryString": false,
                    "Cookies": {"Forward": "none"}
                }
            },
            {
                "PathPattern": "images/*",
                "TargetOriginId": "S3-$BUCKET_NAME",
                "ViewerProtocolPolicy": "redirect-to-https",
                "MinTTL": 0,
                "DefaultTTL": 2592000,
                "MaxTTL": 31536000,
                "Compress": true,
                "ForwardedValues": {
                    "QueryString": true,
                    "Cookies": {"Forward": "none"},
                    "Headers": {
                        "Quantity": 2,
                        "Items": ["Accept", "CloudFront-Is-Mobile-Viewer"]
                    }
                }
            },
            {
                "PathPattern": "fonts/*",
                "TargetOriginId": "S3-$BUCKET_NAME",
                "ViewerProtocolPolicy": "redirect-to-https",
                "MinTTL": 31536000,
                "DefaultTTL": 31536000,
                "MaxTTL": 31536000,
                "Compress": false,
                "ForwardedValues": {
                    "QueryString": false,
                    "Cookies": {"Forward": "none"}
                }
            }
        ]
    },
    "Enabled": true,
    "PriceClass": "PriceClass_All",
    "HttpVersion": "http2",
    "IsIPV6Enabled": true
}
EOF
    
    # Créer la distribution
    DISTRIBUTION_ID=$(aws cloudfront create-distribution --distribution-config file://"$DISTRIBUTION_CONFIG" --query 'Distribution.Id' --output text)
    
    if [ $? -eq 0 ]; then
        success "Distribution CloudFront créée: $DISTRIBUTION_ID"
        log "Configuration en cours... Cela peut prendre 15-20 minutes."
        
        # Obtenir le nom de domaine CloudFront
        CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution --id "$DISTRIBUTION_ID" --query 'Distribution.DomainName' --output text)
        success "Distribution créée: https://$CLOUDFRONT_DOMAIN"
        
        # Sauvegarder les informations
        cat > cdn-info.json << EOF
{
    "distributionId": "$DISTRIBUTION_ID",
    "domain": "$CLOUDFRONT_DOMAIN",
    "customDomain": "$DOMAIN_NAME",
    "bucket": "$BUCKET_NAME",
    "region": "$REGION",
    "environment": "$ENVIRONMENT",
    "createdAt": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
        
        success "Informations CDN sauvegardées dans cdn-info.json"
    else
        error "Échec de la création de la distribution CloudFront"
        exit 1
    fi
}

# Nettoyer les fichiers temporaires
cleanup() {
    log "Nettoyage des fichiers temporaires..."
    rm -f cors-config.json lifecycle-config.json bucket-policy.json
    success "Nettoyage terminé"
}

# Fonction principale
main() {
    log "Démarrage de la configuration CDN optimisée..."
    
    check_prerequisites
    setup_s3_bucket
    setup_cloudfront
    cleanup
    
    success "Configuration CDN terminée avec succès !"
    echo ""
    echo "📋 Résumé de la configuration:"
    echo "   🪣 Bucket S3: $BUCKET_NAME"
    echo "   ☁️  Distribution CloudFront: $(cat cdn-info.json | jq -r '.distributionId')"
    echo "   🌐 Domaine CloudFront: $(cat cdn-info.json | jq -r '.domain')"
    echo "   🔗 URL CDN: https://$(cat cdn-info.json | jq -r '.domain')"
    echo ""
    echo "⚡ Optimisations activées:"
    echo "   ✅ Compression Gzip/Brotli"
    echo "   ✅ Cache intelligent par type de fichier"
    echo "   ✅ HTTP/2 et IPv6"
    echo "   ✅ Redirection HTTPS automatique"
    echo "   ✅ Headers optimisés pour images responsives"
    echo ""
    echo "📝 Prochaines étapes:"
    echo "   1. Configurer le DNS pour pointer $DOMAIN_NAME vers $(cat cdn-info.json | jq -r '.domain')"
    echo "   2. Uploader vos assets optimisés vers le bucket S3"
    echo "   3. Tester les performances avec Lighthouse"
}

# Gestion des signaux
trap cleanup EXIT

# Exécution
main "$@"

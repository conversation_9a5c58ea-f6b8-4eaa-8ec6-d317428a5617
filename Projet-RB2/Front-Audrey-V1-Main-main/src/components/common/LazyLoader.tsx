/**
 * Composant LazyLoader - Phase 3 Sprint 3.1
 * Date de création: 29 Mai 2025
 *
 * Système de lazy loading intelligent avec :
 * - Preloading conditionnel
 * - Fallbacks optimisés
 * - Gestion d'erreurs avancée
 * - Métriques de performance
 */

import React, { Suspense, lazy, ComponentType, ReactNode, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// Types
interface LazyLoaderProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  preload?: boolean;
  delay?: number;
  timeout?: number;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

interface LazyComponentOptions {
  preload?: boolean;
  chunkName?: string;
  webpackPrefetch?: boolean;
  webpackPreload?: boolean;
}

interface PerformanceMetrics {
  loadTime: number;
  chunkSize?: number;
  cacheHit: boolean;
}

// Cache pour les composants chargés
const componentCache = new Map<string, ComponentType<any>>();
const loadingPromises = new Map<string, Promise<ComponentType<any>>>();
const performanceMetrics = new Map<string, PerformanceMetrics>();

/**
 * Fallback de chargement par défaut
 */
const DefaultLoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-3 text-gray-600">Chargement...</span>
  </div>
);

/**
 * Fallback d'erreur par défaut
 */
const DefaultErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({
  error,
  resetErrorBoundary,
}) => (
  <div className="flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg">
    <div className="text-red-600 mb-4">
      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
        />
      </svg>
    </div>
    <h3 className="text-lg font-semibold text-red-800 mb-2">Erreur de chargement</h3>
    <p className="text-red-600 text-center mb-4">
      Une erreur s'est produite lors du chargement du composant.
    </p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
    >
      Réessayer
    </button>
    {process.env.NODE_ENV === 'development' && (
      <details className="mt-4 text-sm text-red-700">
        <summary className="cursor-pointer">Détails de l'erreur</summary>
        <pre className="mt-2 p-2 bg-red-100 rounded text-xs overflow-auto">
          {error.message}
          {error.stack}
        </pre>
      </details>
    )}
  </div>
);

/**
 * Hook pour gérer le preloading
 */
const usePreload = (
  importFn: () => Promise<{ default: ComponentType<any> }>,
  shouldPreload: boolean,
  delay: number = 0
) => {
  useEffect(() => {
    if (!shouldPreload) return;

    const timer = setTimeout(() => {
      // Précharger le composant sans l'afficher
      importFn().catch(console.error);
    }, delay);

    return () => clearTimeout(timer);
  }, [importFn, shouldPreload, delay]);
};

/**
 * Fonction utilitaire pour créer un composant lazy avec options avancées
 */
export const createLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyComponentOptions = {}
): ComponentType<any> => {
  const { preload = false, chunkName } = options;
  const cacheKey = chunkName || importFn.toString();

  // Vérifier le cache
  if (componentCache.has(cacheKey)) {
    return componentCache.get(cacheKey)!;
  }

  // Créer le composant lazy avec métriques
  const LazyComponent = lazy(async () => {
    const startTime = performance.now();
    let cacheHit = false;

    try {
      // Vérifier si le chargement est déjà en cours
      if (loadingPromises.has(cacheKey)) {
        cacheHit = true;
        const component = await loadingPromises.get(cacheKey)!;
        return { default: component };
      }

      // Créer la promesse de chargement
      const loadingPromise = importFn().then(module => {
        const endTime = performance.now();
        const loadTime = endTime - startTime;

        // Stocker les métriques
        performanceMetrics.set(cacheKey, {
          loadTime,
          cacheHit,
        });

        // Mettre en cache le composant
        componentCache.set(cacheKey, module.default);
        loadingPromises.delete(cacheKey);

        // Log des métriques en développement
        if (process.env.NODE_ENV === 'development') {
          console.log(`[LazyLoader] ${chunkName || 'Component'} loaded in ${loadTime.toFixed(2)}ms`);
        }

        return module.default;
      });

      loadingPromises.set(cacheKey, loadingPromise);
      const component = await loadingPromise;

      return { default: component };
    } catch (error) {
      loadingPromises.delete(cacheKey);
      throw error;
    }
  });

  // Ajouter le preloading si nécessaire
  if (preload) {
    // Précharger après un délai
    setTimeout(() => {
      importFn().catch(console.error);
    }, 100);
  }

  return LazyComponent;
};

/**
 * Composant LazyLoader principal
 */
export const LazyLoader: React.FC<LazyLoaderProps> = ({
  children,
  fallback = <DefaultLoadingFallback />,
  errorFallback = DefaultErrorFallback,
  preload = false,
  delay = 0,
  timeout = 10000,
  onLoad,
  onError,
}) => {
  const [hasTimedOut, setHasTimedOut] = useState(false);

  // Gérer le timeout
  useEffect(() => {
    if (timeout > 0) {
      const timer = setTimeout(() => {
        setHasTimedOut(true);
        onError?.(new Error('Component loading timeout'));
      }, timeout);

      return () => clearTimeout(timer);
    }
  }, [timeout, onError]);

  // Fallback de timeout
  const TimeoutFallback = () => (
    <div className="flex flex-col items-center justify-center p-8 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="text-yellow-600 mb-4">
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-yellow-800 mb-2">Chargement lent</h3>
      <p className="text-yellow-600 text-center">
        Le composant prend plus de temps que prévu à charger...
      </p>
    </div>
  );

  if (hasTimedOut) {
    return <TimeoutFallback />;
  }

  return (
    <ErrorBoundary
      FallbackComponent={errorFallback}
      onError={onError}
      onReset={() => setHasTimedOut(false)}
    >
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

/**
 * Hook pour obtenir les métriques de performance
 */
export const usePerformanceMetrics = () => {
  return {
    getMetrics: (chunkName?: string) => {
      if (chunkName) {
        return performanceMetrics.get(chunkName);
      }
      return Object.fromEntries(performanceMetrics.entries());
    },
    clearMetrics: () => {
      performanceMetrics.clear();
    },
    getTotalLoadTime: () => {
      return Array.from(performanceMetrics.values())
        .reduce((total, metric) => total + metric.loadTime, 0);
    },
  };
};

/**
 * Composant pour afficher les métriques de performance (dev uniquement)
 */
export const PerformanceDebugger: React.FC = () => {
  const { getMetrics } = usePerformanceMetrics();
  const [metrics, setMetrics] = useState<Record<string, PerformanceMetrics>>({});

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(getMetrics() as Record<string, PerformanceMetrics>);
    }, 1000);

    return () => clearInterval(interval);
  }, [getMetrics]);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm">
      <h4 className="font-bold mb-2">Lazy Loading Metrics</h4>
      {Object.entries(metrics).map(([key, metric]) => (
        <div key={key} className="mb-1">
          <span className="font-mono">{key}:</span>
          <span className="ml-2">{metric.loadTime.toFixed(2)}ms</span>
          {metric.cacheHit && <span className="ml-1 text-green-400">(cached)</span>}
        </div>
      ))}
    </div>
  );
};

// Exports principaux
export { LazyLoader, createLazyComponent, usePerformanceMetrics, PerformanceDebugger };
export default LazyLoader;

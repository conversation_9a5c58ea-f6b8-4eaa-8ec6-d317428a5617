/**
 * Composant OptimizedImage - Phase 3 Sprint 3.1
 * Date de création: 29 Mai 2025
 * 
 * Composant d'image optimisé avec :
 * - Lazy loading intelligent
 * - Formats d'images modernes (WebP, AVIF)
 * - Images responsives automatiques
 * - Placeholder et transitions fluides
 * - Gestion d'erreurs avancée
 */

import React, { useState, useRef, useEffect, ImgHTMLAttributes } from 'react';
import { useCDN } from '@/utils/cdnManager';

interface OptimizedImageProps extends Omit<ImgHTMLAttributes<HTMLImageElement>, 'src' | 'srcSet'> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'jpg' | 'png' | 'auto';
  lazy?: boolean;
  priority?: 'high' | 'medium' | 'low';
  responsive?: boolean;
  responsiveWidths?: number[];
  placeholder?: 'blur' | 'color' | 'skeleton' | React.ReactNode;
  placeholderColor?: string;
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: (error: Error) => void;
  fallbackSrc?: string;
  className?: string;
  containerClassName?: string;
}

interface ImageState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  isInView: boolean;
}

/**
 * Hook pour l'intersection observer (lazy loading)
 */
const useIntersectionObserver = (
  ref: React.RefObject<HTMLElement>,
  options: IntersectionObserverInit = {}
) => {
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(ref.current);

    return () => observer.disconnect();
  }, [ref, options]);

  return isInView;
};

/**
 * Composant de placeholder blur
 */
const BlurPlaceholder: React.FC<{ 
  dataURL?: string; 
  width?: number; 
  height?: number;
  className?: string;
}> = ({ dataURL, width, height, className }) => {
  if (!dataURL) {
    return (
      <div 
        className={`bg-gray-200 animate-pulse ${className}`}
        style={{ width, height }}
      />
    );
  }

  return (
    <img
      src={dataURL}
      alt=""
      className={`filter blur-sm scale-110 transition-all duration-300 ${className}`}
      style={{ width, height }}
      aria-hidden="true"
    />
  );
};

/**
 * Composant de placeholder skeleton
 */
const SkeletonPlaceholder: React.FC<{ 
  width?: number; 
  height?: number;
  className?: string;
}> = ({ width, height, className }) => (
  <div 
    className={`bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse ${className}`}
    style={{ 
      width, 
      height,
      backgroundSize: '200% 100%',
      animation: 'shimmer 1.5s infinite',
    }}
  >
    <style jsx>{`
      @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
      }
    `}</style>
  </div>
);

/**
 * Composant OptimizedImage principal
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  quality = 85,
  format = 'auto',
  lazy = true,
  priority = 'medium',
  responsive = true,
  responsiveWidths = [400, 800, 1200, 1600],
  placeholder = 'blur',
  placeholderColor = '#f3f4f6',
  blurDataURL,
  onLoad,
  onError,
  fallbackSrc,
  className = '',
  containerClassName = '',
  ...imgProps
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const { getAssetUrl, generateSrcSet, generatePictureAttributes } = useCDN();

  const [state, setState] = useState<ImageState>({
    isLoading: true,
    isLoaded: false,
    hasError: false,
    isInView: false,
  });

  // Lazy loading avec intersection observer
  const isInView = useIntersectionObserver(containerRef, {
    threshold: 0.1,
    rootMargin: '50px',
  });

  // Mettre à jour l'état de visibilité
  useEffect(() => {
    if (isInView) {
      setState(prev => ({ ...prev, isInView: true }));
    }
  }, [isInView]);

  // Générer les URLs optimisées
  const optimizedSrc = getAssetUrl(src, {
    format,
    quality,
    width,
    height,
    priority,
  });

  const srcSet = responsive && width
    ? generateSrcSet(src, responsiveWidths, { format, quality, priority })
    : undefined;

  const pictureAttributes = generatePictureAttributes(src, {
    format: format === 'auto' ? undefined : format,
    quality,
    width,
    height,
    priority,
  });

  // Gestionnaires d'événements
  const handleLoad = () => {
    setState(prev => ({ ...prev, isLoading: false, isLoaded: true }));
    onLoad?.();
  };

  const handleError = (error: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const errorObj = new Error(`Failed to load image: ${src}`);
    setState(prev => ({ ...prev, isLoading: false, hasError: true }));
    onError?.(errorObj);

    // Essayer le fallback si disponible
    if (fallbackSrc && imgRef.current) {
      imgRef.current.src = fallbackSrc;
    }
  };

  // Rendu du placeholder
  const renderPlaceholder = () => {
    if (typeof placeholder === 'object') {
      return placeholder;
    }

    const placeholderProps = {
      width,
      height,
      className: `absolute inset-0 ${className}`,
    };

    switch (placeholder) {
      case 'blur':
        return <BlurPlaceholder dataURL={blurDataURL} {...placeholderProps} />;
      case 'skeleton':
        return <SkeletonPlaceholder {...placeholderProps} />;
      case 'color':
        return (
          <div
            className={`absolute inset-0 ${className}`}
            style={{ backgroundColor: placeholderColor, width, height }}
          />
        );
      default:
        return null;
    }
  };

  // Ne pas charger l'image si lazy loading et pas encore visible
  const shouldLoadImage = !lazy || state.isInView || priority === 'high';

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${containerClassName}`}
      style={{ width, height }}
    >
      {/* Placeholder */}
      {state.isLoading && !state.hasError && renderPlaceholder()}

      {/* Image principale */}
      {shouldLoadImage && (
        <picture>
          {/* Sources pour différents formats */}
          {pictureAttributes.map((attrs, index) => (
            <source
              key={index}
              srcSet={responsive ? generateSrcSet(src, responsiveWidths, {
                format: attrs.type.split('/')[1] as any,
                quality,
                priority,
              }) : attrs.srcSet}
              type={attrs.type}
              sizes={responsive ? '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw' : undefined}
            />
          ))}
          
          {/* Image de fallback */}
          <img
            ref={imgRef}
            src={optimizedSrc}
            srcSet={srcSet}
            sizes={responsive ? '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw' : undefined}
            alt={alt}
            width={width}
            height={height}
            onLoad={handleLoad}
            onError={handleError}
            className={`
              transition-opacity duration-300
              ${state.isLoaded ? 'opacity-100' : 'opacity-0'}
              ${state.hasError ? 'hidden' : ''}
              ${className}
            `}
            loading={lazy && priority !== 'high' ? 'lazy' : 'eager'}
            decoding="async"
            {...imgProps}
          />
        </picture>
      )}

      {/* Fallback d'erreur */}
      {state.hasError && (
        <div className={`absolute inset-0 flex items-center justify-center bg-gray-100 ${className}`}>
          <div className="text-center text-gray-500">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-sm">Image non disponible</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;

/**
 * CDN Manager - Phase 3 Sprint 3.1
 * Date de création: 29 Mai 2025
 * 
 * Gestionnaire intelligent pour CDN avec :
 * - Détection automatique du meilleur CDN
 * - Fallback sur CDN secondaire
 * - Cache intelligent
 * - Optimisation des formats d'images
 * - Preloading adaptatif
 */

interface CDNConfig {
  primary: string;
  secondary?: string;
  regions: Record<string, string>;
  enableAutoDetection: boolean;
  cacheTimeout: number;
  supportedFormats: string[];
}

interface AssetOptions {
  format?: 'webp' | 'avif' | 'jpg' | 'png' | 'auto';
  quality?: number;
  width?: number;
  height?: number;
  lazy?: boolean;
  priority?: 'high' | 'medium' | 'low';
}

interface CDNMetrics {
  latency: number;
  successRate: number;
  lastChecked: number;
  region: string;
}

class CDNManager {
  private config: CDNConfig;
  private cache: Map<string, string> = new Map();
  private metrics: Map<string, CDNMetrics> = new Map();
  private currentCDN: string;
  private userRegion: string | null = null;

  constructor(config: CDNConfig) {
    this.config = config;
    this.currentCDN = config.primary;
    this.initializeRegionDetection();
  }

  /**
   * Initialise la détection de région
   */
  private async initializeRegionDetection(): Promise<void> {
    if (!this.config.enableAutoDetection) return;

    try {
      // Détecter la région de l'utilisateur
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      this.userRegion = data.country_code?.toLowerCase();

      // Sélectionner le CDN optimal pour la région
      if (this.userRegion && this.config.regions[this.userRegion]) {
        this.currentCDN = this.config.regions[this.userRegion];
      }

      // Tester la latence des CDN disponibles
      await this.benchmarkCDNs();
    } catch (error) {
      console.warn('[CDNManager] Échec de la détection de région:', error);
    }
  }

  /**
   * Teste la latence des CDN disponibles
   */
  private async benchmarkCDNs(): Promise<void> {
    const cdnUrls = [
      this.config.primary,
      ...(this.config.secondary ? [this.config.secondary] : []),
      ...Object.values(this.config.regions),
    ];

    const uniqueCDNs = [...new Set(cdnUrls)];

    for (const cdnUrl of uniqueCDNs) {
      try {
        const startTime = performance.now();
        const response = await fetch(`${cdnUrl}/health-check.json`, {
          method: 'HEAD',
          cache: 'no-cache',
        });
        const endTime = performance.now();

        if (response.ok) {
          this.metrics.set(cdnUrl, {
            latency: endTime - startTime,
            successRate: 1.0,
            lastChecked: Date.now(),
            region: this.userRegion || 'unknown',
          });
        }
      } catch (error) {
        this.metrics.set(cdnUrl, {
          latency: Infinity,
          successRate: 0.0,
          lastChecked: Date.now(),
          region: this.userRegion || 'unknown',
        });
      }
    }

    // Sélectionner le CDN avec la meilleure latence
    const bestCDN = this.getBestCDN();
    if (bestCDN) {
      this.currentCDN = bestCDN;
    }
  }

  /**
   * Retourne le meilleur CDN basé sur les métriques
   */
  private getBestCDN(): string | null {
    let bestCDN: string | null = null;
    let bestScore = Infinity;

    for (const [cdnUrl, metrics] of this.metrics.entries()) {
      // Score basé sur latence et taux de succès
      const score = metrics.latency / metrics.successRate;
      if (score < bestScore) {
        bestScore = score;
        bestCDN = cdnUrl;
      }
    }

    return bestCDN;
  }

  /**
   * Génère l'URL optimisée pour un asset
   */
  public getAssetUrl(path: string, options: AssetOptions = {}): string {
    const cacheKey = `${path}-${JSON.stringify(options)}`;
    
    // Vérifier le cache
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    let url = `${this.currentCDN}${path.startsWith('/') ? '' : '/'}${path}`;

    // Ajouter les paramètres d'optimisation
    const params = new URLSearchParams();

    // Format d'image
    if (options.format) {
      if (options.format === 'auto') {
        params.append('format', this.getBestImageFormat());
      } else {
        params.append('format', options.format);
      }
    }

    // Qualité
    if (options.quality) {
      params.append('quality', options.quality.toString());
    }

    // Dimensions
    if (options.width) {
      params.append('width', options.width.toString());
    }
    if (options.height) {
      params.append('height', options.height.toString());
    }

    // Ajouter les paramètres à l'URL
    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    // Mettre en cache
    this.cache.set(cacheKey, url);

    // Preloading si priorité haute
    if (options.priority === 'high') {
      this.preloadAsset(url);
    }

    return url;
  }

  /**
   * Détermine le meilleur format d'image supporté
   */
  private getBestImageFormat(): string {
    // Vérifier le support AVIF
    if (this.supportsFormat('avif')) {
      return 'avif';
    }

    // Vérifier le support WebP
    if (this.supportsFormat('webp')) {
      return 'webp';
    }

    // Fallback sur JPEG
    return 'jpg';
  }

  /**
   * Vérifie si un format d'image est supporté
   */
  private supportsFormat(format: string): boolean {
    if (typeof window === 'undefined') return false;

    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;

    try {
      return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0;
    } catch {
      return false;
    }
  }

  /**
   * Preload un asset
   */
  private preloadAsset(url: string): void {
    if (typeof window === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    // Déterminer le type de ressource
    if (url.match(/\.(jpg|jpeg|png|webp|avif)$/i)) {
      link.as = 'image';
    } else if (url.match(/\.(woff|woff2|ttf|otf)$/i)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    } else if (url.match(/\.(css)$/i)) {
      link.as = 'style';
    } else if (url.match(/\.(js)$/i)) {
      link.as = 'script';
    }

    document.head.appendChild(link);
  }

  /**
   * Génère un srcset pour images responsives
   */
  public generateSrcSet(
    path: string,
    widths: number[],
    options: Omit<AssetOptions, 'width'> = {}
  ): string {
    return widths
      .map(width => {
        const url = this.getAssetUrl(path, { ...options, width });
        return `${url} ${width}w`;
      })
      .join(', ');
  }

  /**
   * Génère les attributs picture pour formats multiples
   */
  public generatePictureAttributes(
    path: string,
    options: AssetOptions = {}
  ): Array<{ srcSet: string; type: string }> {
    const formats = ['avif', 'webp', 'jpg'] as const;
    const supportedFormats = formats.filter(format => 
      this.config.supportedFormats.includes(format)
    );

    return supportedFormats.map(format => ({
      srcSet: this.getAssetUrl(path, { ...options, format }),
      type: `image/${format}`,
    }));
  }

  /**
   * Invalide le cache
   */
  public invalidateCache(pattern?: string): void {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * Retourne les métriques actuelles
   */
  public getMetrics(): Record<string, CDNMetrics> {
    return Object.fromEntries(this.metrics.entries());
  }

  /**
   * Force le changement de CDN
   */
  public switchCDN(cdnUrl: string): void {
    this.currentCDN = cdnUrl;
    this.invalidateCache();
  }

  /**
   * Retourne le CDN actuellement utilisé
   */
  public getCurrentCDN(): string {
    return this.currentCDN;
  }
}

// Configuration par défaut
const defaultConfig: CDNConfig = {
  primary: process.env.VITE_CDN_PRIMARY || 'https://cdn.retreatandbe.com',
  secondary: process.env.VITE_CDN_SECONDARY,
  regions: {
    us: process.env.VITE_CDN_US || 'https://us-cdn.retreatandbe.com',
    eu: process.env.VITE_CDN_EU || 'https://eu-cdn.retreatandbe.com',
    ap: process.env.VITE_CDN_AP || 'https://ap-cdn.retreatandbe.com',
  },
  enableAutoDetection: true,
  cacheTimeout: 3600000, // 1 heure
  supportedFormats: ['avif', 'webp', 'jpg', 'png'],
};

// Instance singleton
export const cdnManager = new CDNManager(defaultConfig);

// Hooks React pour utilisation dans les composants
export const useCDN = () => {
  return {
    getAssetUrl: (path: string, options?: AssetOptions) => 
      cdnManager.getAssetUrl(path, options),
    generateSrcSet: (path: string, widths: number[], options?: Omit<AssetOptions, 'width'>) =>
      cdnManager.generateSrcSet(path, widths, options),
    generatePictureAttributes: (path: string, options?: AssetOptions) =>
      cdnManager.generatePictureAttributes(path, options),
    getCurrentCDN: () => cdnManager.getCurrentCDN(),
    getMetrics: () => cdnManager.getMetrics(),
  };
};

export default CDNManager;

/**
 * Configuration Vite Optimisée - Phase 3 : Performance Frontend
 * Date de création: 29 Mai 2025
 * 
 * Configuration avancée pour optimisation des performances :
 * - Bundle splitting intelligent
 * - Lazy loading automatique
 * - Optimisation des assets
 * - CDN ready
 * - Tree shaking avancé
 */

import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { compression } from 'vite-plugin-compression';
import { createHtmlPlugin } from 'vite-plugin-html';

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';
  const isDevelopment = mode === 'development';

  return {
    plugins: [
      // Plugin React avec optimisations
      react({
        // Fast Refresh optimisé
        fastRefresh: isDevelopment,
        // Babel optimisations
        babel: {
          plugins: [
            // Optimisation des imports
            ['import', { libraryName: 'lodash', libraryDirectory: '', camel2DashComponentName: false }, 'lodash'],
            ['import', { libraryName: 'antd', libraryDirectory: 'es', style: true }, 'antd'],
          ],
        },
      }),

      // Plugin HTML avec optimisations
      createHtmlPlugin({
        minify: isProduction,
        inject: {
          data: {
            title: 'Retreat And Be - Optimized',
            description: 'Plateforme de retraites optimisée pour les performances',
          },
        },
      }),

      // Compression Gzip/Brotli
      compression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 1024,
        deleteOriginFile: false,
      }),
      compression({
        algorithm: 'brotliCompress',
        ext: '.br',
        threshold: 1024,
        deleteOriginFile: false,
      }),

      // Analyseur de bundle (production uniquement)
      isProduction && visualizer({
        filename: 'dist/bundle-analysis.html',
        open: false,
        gzipSize: true,
        brotliSize: true,
      }),
    ].filter(Boolean),

    // Configuration du serveur de développement
    server: {
      port: 3000,
      host: true,
      strictPort: false,
      open: false,
      cors: true,
      // HMR optimisé
      hmr: {
        overlay: true,
        clientPort: 3000,
      },
      // Proxy pour l'API
      proxy: {
        '/api': {
          target: env.VITE_API_URL || 'http://localhost:3001',
          changeOrigin: true,
          secure: false,
        },
      },
    },

    // Configuration de build optimisée
    build: {
      target: 'es2020',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: isDevelopment ? true : 'hidden',
      minify: isProduction ? 'terser' : false,
      
      // Configuration Terser pour minification avancée
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? ['console.log', 'console.info'] : [],
        },
        mangle: {
          safari10: true,
        },
        format: {
          comments: false,
        },
      },

      // Configuration Rollup pour bundle splitting
      rollupOptions: {
        output: {
          // Chunking strategy optimisée
          manualChunks: {
            // Vendor chunks
            'vendor-react': ['react', 'react-dom', 'react-router-dom'],
            'vendor-ui': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', 'lucide-react'],
            'vendor-utils': ['lodash', 'date-fns', 'clsx', 'tailwind-merge'],
            'vendor-forms': ['react-hook-form', '@hookform/resolvers', 'zod'],
            'vendor-state': ['zustand', '@tanstack/react-query'],
            'vendor-charts': ['recharts', 'chart.js'],
            
            // Feature chunks
            'feature-auth': [
              './src/modules/auth',
              './src/components/auth',
              './src/hooks/useAuthContext.ts',
            ],
            'feature-dashboard': [
              './src/modules/dashboard',
              './src/pages/Dashboard.tsx',
            ],
            'feature-retreats': [
              './src/modules/retreats',
              './src/components/RetreatFinder',
            ],
            'feature-professionals': [
              './src/modules/professionals',
              './src/pages/ProfessionalHomePage.tsx',
            ],
          },
          
          // Nommage des chunks
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
              ? chunkInfo.facadeModuleId.split('/').pop().replace(/\.[^/.]+$/, '')
              : 'chunk';
            return `js/${facadeModuleId}-[hash].js`;
          },
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
              return `images/[name]-[hash].${ext}`;
            }
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
              return `fonts/[name]-[hash].${ext}`;
            }
            return `assets/[name]-[hash].${ext}`;
          },
        },
        
        // Optimisations externes
        external: (id) => {
          // Externaliser les dépendances CDN en production
          if (isProduction && env.VITE_USE_CDN === 'true') {
            return ['react', 'react-dom'].includes(id);
          }
          return false;
        },
      },

      // Optimisation des assets
      assetsInlineLimit: 4096, // 4KB
      cssCodeSplit: true,
      
      // Configuration du chunk size
      chunkSizeWarningLimit: 1000,
    },

    // Optimisation des dépendances
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'zustand',
        'lodash',
        'date-fns',
        'clsx',
        'tailwind-merge',
      ],
      exclude: [
        // Exclure les modules qui causent des problèmes
        '@storybook/addon-docs',
      ],
      esbuildOptions: {
        target: 'es2020',
        supported: {
          bigint: true,
        },
      },
    },

    // Configuration des alias
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@/components': resolve(__dirname, './src/components'),
        '@/utils': resolve(__dirname, './src/utils'),
        '@/store': resolve(__dirname, './src/store'),
        '@/hooks': resolve(__dirname, './src/hooks'),
        '@/types': resolve(__dirname, './src/types'),
        '@/modules': resolve(__dirname, './src/modules'),
        '@/pages': resolve(__dirname, './src/pages'),
        '@/services': resolve(__dirname, './src/services'),
        '@/styles': resolve(__dirname, './src/styles'),
        '@/assets': resolve(__dirname, './src/assets'),
      },
    },

    // Configuration CSS
    css: {
      modules: {
        localsConvention: 'camelCase',
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`,
        },
      },
      postcss: {
        plugins: [
          require('tailwindcss'),
          require('autoprefixer'),
          ...(isProduction ? [require('cssnano')] : []),
        ],
      },
    },

    // Variables d'environnement
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __IS_PRODUCTION__: isProduction,
    },

    // Configuration ESBuild
    esbuild: {
      target: 'es2020',
      drop: isProduction ? ['console', 'debugger'] : [],
      legalComments: 'none',
    },

    // Configuration du worker
    worker: {
      format: 'es',
      plugins: [react()],
    },

    // Configuration de la preview
    preview: {
      port: 4173,
      host: true,
      strictPort: false,
      open: false,
    },
  };
});
